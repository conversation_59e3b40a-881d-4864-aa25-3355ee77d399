<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="aircraft" cnName="战车定义">
		<equip name="BlueWhale" cnName="巨鲸" evolutionLabel="TheEagle">
			<canComposeB>1</canComposeB>
			<main dpsMul="1.3" len="81" minRa="-180" maxRa="-179.9"/>
			<sub dpsMul="0.9" len="35" minRa="179.9" maxRa="180"/>
			<lifeMul>0.75</lifeMul>
			<attackMul>0</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>110</mustCash>
			<addObjJson>{'dpsAll':0.05,'lifeAll':0.05}</addObjJson>
		</equip>
		<equip name="TheEagle" cnName="雄鹰" evolutionLabel="TheRoc">
			<evolutionLv>2</evolutionLv>
			<main label="BlueWhale_main" dpsMul="1.8" len="81" minRa="-180" maxRa="-179.9"/>
			<sub label="BlueWhale_sub" dpsMul="1.2" len="35" minRa="179.9" maxRa="180"/>
			<lifeMul>0.75</lifeMul>
			<attackMul>0</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>110</mustCash>
			<addObjJson>{'dpsAll':0.08,'lifeAll':0.08}</addObjJson>
			<skillArr>vehicleFit_fly</skillArr>
		</equip>
		<equip name="TheRoc" cnName="大鹏" evolutionLabel="TheKun">
			<evolutionLv>4</evolutionLv>
			<main label="BlueWhale_main" dpsMul="2.4" len="81" minRa="-180" maxRa="-179.9"/>
			<sub label="BlueWhale_sub" dpsMul="1.6" len="35" minRa="179.9" maxRa="180"/>
			<lifeMul>1.2</lifeMul>
			<attackMul>0</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>130</mustCash>
			<addObjJson>{'dpsAll':0.10,'lifeAll':0.10}</addObjJson>
			<skillArr>vehicleFit_fly</skillArr>
		</equip>
		<equip name="TheKun" cnName="鲲">
			<evolutionLv>5</evolutionLv>
			<main label="BlueWhale_main" dpsMul="2.8" len="86" minRa="-180" maxRa="-179.9"/>
			<sub label="BlueWhale_sub" dpsMul="2" len="30" minRa="179.9" maxRa="180"/>
			<lifeMul>2</lifeMul>
			<attackMul>0</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>130</mustCash>
			<addObjJson>{'dpsAll':0.16,'lifeAll':0.16}</addObjJson>
			<skillArr>theKunSkill,vehicleFit_fly</skillArr>
		</equip>
		
		
		<bullet cnName="巨鲸-主炮">
			<name>BlueWhale_main</name>
			<cnName>巨鲸-主炮</cnName>
			<!--武器属性------------------------------------------------------------ -->
			<hurtRatio>0.31</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>1</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.7</attackGap>
			<bulletNum>3</bulletNum>
			<shootAngle>5</shootAngle>
			<!--运动属性------------------------------------------------------------ -->	
			<shootRecoil>15</shootRecoil>
			<screenShakeValue>16</screenShakeValue>
			<bulletSpeed>35</bulletSpeed>
			<boomD  bodyB="1" floorB="1" radius="120"  maxHurtNum="99"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">rocket/rocketBullet</bulletImgUrl>
			<fireImgUrl raNum="30" soundUrl="rocket/barrel2_sound" con="add">gunFire/rocket</fireImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom1"  shake="2,0.2,10">boomEffect/boom3</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="巨鲸-副炮">
			<name>BlueWhale_sub</name>
			<cnName>巨鲸-副炮</cnName>
			<!--武器属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>0.001</bulletLife>
			<hitType>longLine</hitType>
			<bulletWidth>800</bulletWidth>
			<bulletShakeWidth>100</bulletShakeWidth>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.15</attackGap>
			<attackDelay>0</attackDelay>
			<!--运动属性------------------------------------------------------------ -->	
			<shootRecoil>4</shootRecoil>
			<screenShakeValue>9</screenShakeValue>
			<shakeAngle>3</shakeAngle>
			<bulletSpeed>0</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<lineD lightColor="0xFFFF00" size="2" lightSize="6"/>
			<bulletImgUrl>longLine</bulletImgUrl>
			<fireImgUrl raNum="30" soundUrl="ak/barrel2_sound">gunFire/f</fireImgUrl>
			<hitImgUrl>bulletHitEffect/yellow_motion</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
	</father>
	<father name="vehicleSkill" cnName="技能">
		<skill>
			<name>theKunSkill</name>
			<cnName>弹药回收</cnName><iconUrl>SkillIcon/theKunSkill</iconUrl>
			<effectInfoArr>补充弹药</effectInfoArr>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>carShoot</condition>
			<target>me</target>
			<changeText>概率[effectProArr.0]</changeText>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>theKunSkill</effectType><effectFather>vehicle</effectFather>
			<effectProArr>0.04</effectProArr>
			<valueString>addCharger</valueString>
			<description>每次射击都有[effectProArr.0]的概率产生若干个（数量和子弹数相同）持续2分钟的弹药箱。</description>
			<pointEffectImg name="beadCrossbow_boom"/>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><effectProArr>0.006</effectProArr></skill>
				<skill><effectProArr>0.009</effectProArr></skill>
				<skill><effectProArr>0.012</effectProArr></skill>
				<skill><effectProArr>0.015</effectProArr></skill>
				<skill><effectProArr>0.018</effectProArr></skill>
				<skill><effectProArr>0.021</effectProArr></skill>
				<skill><effectProArr>0.024</effectProArr></skill>
				<skill><effectProArr>0.027</effectProArr></skill>
				<skill><effectProArr>0.030</effectProArr></skill>
				<skill><effectProArr>0.040</effectProArr></skill>
			</growth>
		</skill>
	</father>
	
	
	
	
	
	<father name="vehicle" cnName="战车body">
		<body index="0" name="巨鲸" shell="metal">
			
			<name>BlueWhale</name>
			<cnName>巨鲸</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/vehicle/BlueWhale.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>0.5</headHurtMul>
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgClass>CarImage</imgClass>
			<imgType>normal</imgType>
			<rotateBySlopeB>1</rotateBySlopeB>
			<flipCtrlBy>mouse</flipCtrlBy>
			<imgArr>
				stand,move,die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<motionClass>AircraftGroundMotion</motionClass>
			<motionState>fly</motionState>
			<flyType>space</flyType>
			<motionD F_G="0.8" jumpDelayT="0.15" F_I="0.3" F_F="0.9" moveWhenVB="1" F_AIR="3"/>
			<maxVx>12</maxVx>
			<maxJumpNum>1</maxJumpNum>
			<!-- 技能 -->
			<attackAIClass>CarAttack_AI</attackAIClass>
			<keyClass>CarBodyKey</keyClass>
			<bulletLauncherClass>CarBulletLauncher</bulletLauncherClass>
			<skillArr></skillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				
			</hurtArr>
		</body>
		
		<body name="雄鹰" fixed="BlueWhale" shell="metal">
			<name>TheEagle</name>
			<cnName>雄鹰</cnName>
			<swfUrl>swf/vehicle/TheEagle.swf</swfUrl>
			<bmpUrl>BodyImg/TheEagle</bmpUrl>
		</body>
		<body name="大鹏" fixed="BlueWhale" shell="metal">
			<name>TheRoc</name>
			<cnName>大鹏</cnName>
			<swfUrl>swf/vehicle/TheRoc.swf</swfUrl>
			<bmpUrl>BodyImg/TheRoc</bmpUrl>
		</body>
		<body name="鲲" fixed="BlueWhale" shell="metal">
			<name>TheKun</name>
			<cnName>鲲</cnName>
			<swfUrl>swf/vehicle/TheKun342.swf</swfUrl>
			<bmpUrl>BodyImg/TheKun</bmpUrl>
		</body>
	</father>
</data>