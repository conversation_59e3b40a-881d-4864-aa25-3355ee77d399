<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="snake" cnName="血蟒">
		<body index="0" name="小飞天狗" shell="metal">
			
			<name>WatchdogAir</name>
			<cnName>飞天狗</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/enemy/WatchdogAir.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>0.6</lifeRatio>
			<rosRatio>99</rosRatio>
			<showLevel>9999</showLevel>
			<motionState>fly</motionState><flyType>space</flyType>
			<dieImg name="midSpace"/><dieJumpMul>0</dieJumpMul>
			<lifeBarExtraHeight>45</lifeBarExtraHeight>
			<imgArr>
				stand,move
				,shootAttack,machineAttack
				,die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-45,28,45</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>7</maxVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<bossSkillArr>noSpeedReduce,offPassSkill,defenceBounce_enemy</bossSkillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>shootAttack</imgLabel><cn>射击</cn>
					<hurtRatio>1</hurtRatio>
					<bulletLabel>WatchdogAirShoot</bulletLabel>
					<grapRect>-35,114,85,370</grapRect>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>machineAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-400,-111,100,105</grapRect>
				</hurt>
			</hurtArr>
		</body>	
	</father>		
	<father name="enemy">
		<bullet cnName="小飞天狗-射击">
			<name>WatchdogAirShoot</name>
			<cnName>小飞天狗-射击</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>1</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.9</attackGap>
			<attackDelay>0.6</attackDelay>
			<bulletAngle>90</bulletAngle>
			<bulletAngleRange>10</bulletAngleRange>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,34</shootPoint>
			<bulletSpeed>30</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl name="shoot_Watchdog_bullet"/>
			<hitImgUrl name="gun_hit"/>
		</bullet>
	</father>	
	
	
</data>