<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="car" cnName="战车定义">
		<equip name="SaberTigerCar" cnName="异齿虎"  rideLabel="RedMotoRide">
			<evolutionLv>2</evolutionLv>
			<main label="SaberTigerCar_main" dpsMul="2.6" len="105"/>
			<sub label="Diggers_sub" hideB="1" dpsMul="0" />
			<lifeMul>2.6</lifeMul>
			<attackMul>1.6</attackMul>
			<duration>80</duration>
			<cd>110</cd>
			
			<addObjJson>{'dpsAll':0.21,'hurtMul_sniper':0.10,'lottery':15}</addObjJson>
			<skillArr>vehicleFit_Gaia</skillArr>
			<specialInfoArr>对机械体造成额外碾压伤害</specialInfoArr>
			<canComposeB>1</canComposeB><mustCash>100</mustCash>
		</equip>
		
		<bullet cnName="异齿虎主炮">
			<name>SaberTigerCar_main</name>
			<cnName>异齿虎主炮</cnName>
			<!--武器属性------------------------------------------------------------ -->
			<hurtRatio>0.66</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>0.001</bulletLife>
			<hitType>longLine</hitType>
			<bulletWidth>800</bulletWidth>
			<bulletShakeWidth>100</bulletShakeWidth>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.1</attackGap>
			<attackDelay>0</attackDelay>
			<!--运动属性------------------------------------------------------------ -->	
			<shootRecoil>4</shootRecoil>
			<screenShakeValue>9</screenShakeValue>
			<shakeAngle>3</shakeAngle>
			<bulletSpeed>0</bulletSpeed>
			
			<!--特殊------------------------------------------------------------ -->
			<gatlinRange>15</gatlinRange><gatlinNum>9</gatlinNum>
			<penetrationNum>3</penetrationNum>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<lineD lightColor="0x9100FF" size="3" lightSize="8" blendMode="add" type="one" />
			<bulletImgUrl>longLine</bulletImgUrl>
			<fireImgUrl raNum="30" soundUrl="ak/barrel5_sound">gunFire/f</fireImgUrl>
			<hitImgUrl>bulletHitEffect/yellow_motion</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		
	</father>
	<father name="vehicle" cnName="战车body">
		<body shell="compound">
			
			<name>SaberTigerCar</name>
			<cnName>异齿虎坐骑</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/vehicle/SaberTigerCar.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>0.5</headHurtMul>
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgClass>CarImage</imgClass>
			<imgType>normal</imgType>
			<rotateBySlopeB>1</rotateBySlopeB>
			<imgArr>
				stand,move,die1
				,__jumpUp,jumpUp,jumpDown,jumpDown__,jumpUp__jumpDown
				,shakeAttack
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<maxVx>20</maxVx>
			<maxJumpNum>1</maxJumpNum>
			
			<!-- 技能 -->
			<attackAIClass>CarAttack_AI</attackAIClass>
			<keyClass>CarBodyKey</keyClass>
			<bulletLauncherClass>CarBulletLauncher</bulletLauncherClass>
			<skillArr></skillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>move</imgLabel>
					<hurtRatio>0.52</hurtRatio>
					<attackType>through</attackType>
					<skillArr>SaberTigerHit</skillArr>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit2">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpDown</imgLabel>
					<hurtRatio>0.52</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/body_hit">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpDown__</imgLabel>
					<hurtRatio>0.52</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/body_hit">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<![CDATA[
				<hurt>
					<imgLabel>shakeAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>21.5</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				]]>
			</hurtArr>
		</body>
		
		<skill><!-- dps-被动 -->
			<name>SaberTigerHit</name>
			<cnName>酸性腐蚀</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>raceType</otherConditionArr>
			<conditionString>robot</conditionString>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurtNoCondition</effectType>
			<mul>1.6</mul>
			<!--图像------------------------------------------------------------ -->
			<description>对机械体造成额外伤害。</description>
		</skill>
		<![CDATA[
		<skill><!-- dps -->
			<name>SaberTigerHit</name>
			<cnName>虎压</cnName><isInvincibleB>1</isInvincibleB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<effectProArr>0.2</effectProArr>
			<minTriggerT>4</minTriggerT>
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>0.9</duration>
			<meActionLabel>shakeAttack</meActionLabel>
			<!--图像------------------------------------------------------------ -->
			<description>碾压敌人时有一定几率触发“虎压”，对敌人造成40倍的碾压伤害！</description>
		</skill>
		]]>
	</father>
</data>