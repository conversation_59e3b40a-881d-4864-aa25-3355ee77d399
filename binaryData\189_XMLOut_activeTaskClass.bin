<?xml version="1.0" encoding="utf-8" ?>
<data>
	<![CDATA[不用的活动可以直接删除，每次加载存档系统会清理没有定义的任务的。]]>
	<father name="active" cnName="活动"  dayNum="1" buyNum="0" autoUnlockByLevelB="1">
		<task name="fastCar" cnName="极速撞击" lv="50" unlockLv="30" uiShowTime="999999">
			<openD start="2025-7-1" end="2025-8-30 23:59:59"/>
			<conditionText>使用陆地载具撞击所有僵尸。</conditionText>
			<uiConditionText>撞击所有僵尸</uiConditionText>
			<description>使用陆地载具撞击地图内所有的僵尸，期间不能退出载具。任务完成时间越短，获得十年币的数量就越多（最少30个，最多50个）。任务中载具将拥有瞬移技能。</description>
			<!-- 目的地 -->
			<worldMapId>NanTang</worldMapId>
			<levelId>activeTask/fastCar</levelId>
			<gift>base;anniCoin;15</gift>
			<gift>things;bossCardStamp;3</gift>
			<gift>things;pianoGun;2</gift>
			<gift>things;skyArch;2</gift>
			<gift>things;iceCone;2</gift>
			<gift>things;barrenAwn;2</gift>
			<gift>things;partsChest78;3</gift>

		</task>
		<![CDATA[
		<task name="maxSpeedTask" cnName="极限射速" uiShowTime="999999" lv="70" unlockLv="0" moreKillEnemyNumIsMe="1" levelIsTaskLvB="1" tempLvB="1">
			<openD start="2025-4-30" end="2025-5-5" />
			<conditionText>僵尸 [nowNum]/[num]</conditionText>
			<uiConditionText>消灭[num] 个僵尸</uiConditionText>
			<description>在任务中，玩家的武器将获得5倍射速加成效果。</description>
			<!-- 地图 -->
			<worldMapId>BaiLu</worldMapId>
			<levelId>maxSpeedTask</levelId>
			<noEnemyWhenCompleteB>1</noEnemyWhenCompleteB>
			<uiFleshCondition><one>bodyEvent:die; anyone</one></uiFleshCondition>
			<condition type="collect" target="killEnemyNum" value="500" cumulativeType="no" />
			<growth>
				<task lv="70">
					<enemyLifeMul>80</enemyLifeMul>
					<condition type="collect" target="killEnemyNum" value="500" cumulativeType="no" />
					<gift>base;anniCoin;25</gift>
					<gift>things;equipGemChest;14</gift>
					<gift>things;armsGemChest;14</gift>
					<gift>things;demStone;10</gift>
					<gift>things;madheart;10</gift>
				</task>
				<task lv="80">
					<enemyLifeMul>80</enemyLifeMul>
					<condition type="collect" target="killEnemyNum" value="500" cumulativeType="no" />
					<gift>base;anniCoin;26</gift>
					<gift>things;equipGemChest;15</gift>
					<gift>things;armsGemChest;15</gift>
					<gift>things;demStone;10</gift>
					<gift>things;madheart;10</gift>
				</task>
				<task lv="90">
					<enemyLifeMul>80</enemyLifeMul>
					<condition type="collect" target="killEnemyNum" value="500" cumulativeType="no" />
					<gift>base;anniCoin;27</gift>
					<gift>things;equipGemChest;16</gift>
					<gift>things;armsGemChest;16</gift>
					<gift>things;demStone;10</gift>
					<gift>things;madheart;10</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>80</enemyLifeMul>
					<condition type="collect" target="killEnemyNum" value="500" cumulativeType="no" />
					<gift>base;anniCoin;28</gift>
					<gift>things;equipGemChest;17</gift>
					<gift>things;armsGemChest;17</gift>
					<gift>things;demStone;10</gift>
					<gift>things;madheart;10</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>240</enemyLifeMul>
					<condition type="collect" target="killEnemyNum" value="500" cumulativeType="no" />
					<gift>base;anniCoin;27</gift>
					<gift>things;equipGemChest;18</gift>
					<gift>things;armsGemChest;18</gift>
					<gift>things;demStone;10</gift>
					<gift>things;madheart;10</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>600</enemyLifeMul>
					<condition type="collect" target="killEnemyNum" value="500" cumulativeType="no" />
					<gift>base;anniCoin;29</gift>
					<gift>things;equipGemChest;19</gift>
					<gift>things;armsGemChest;19</gift>
					<gift>things;demStone;10</gift>
					<gift>things;madheart;10</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>1800</enemyLifeMul>
					<condition type="collect" target="killEnemyNum" value="500" cumulativeType="no" />
					<gift>base;anniCoin;30</gift>
					<gift>things;equipGemChest;20</gift>
					<gift>things;armsGemChest;20</gift>
					<gift>things;demStone;10</gift>
					<gift>things;madheart;10</gift>
				</task>
			</growth>
				
		</task>
		
		
		
		<task name="anniverTask23" cnName="究极散射" uiShowTime="999999" lv="99" unlockLv="30" moreKillEnemyNumIsMe="1" levelIsTaskLvB="1" tempLvB="1">
			<openD start="2025-1-6" end="2025-2-9 23:59:59"/>
			<shortText>消灭守关首领</shortText>
			<uiConditionText>消灭守关首领</uiConditionText>
			<description>在任务中，玩家将获得“无限馈赠”效果，同时持有武器将获得“超级散射”效果（每种武器每周只能获得1次），任务中只使用一把武器是最佳的策略。</description>
			<!-- 地图 -->
			<worldMapType>random</worldMapType>
			<fixedLevelUrl>activeTask/anniverTask23</fixedLevelUrl>
			<diff>1</diff>
			<growth>
				<task lv="50">
					<enemyLifeMul>20</enemyLifeMul><enemyDpsMul>10</enemyDpsMul>
					<gift>base;anniCoin;12</gift>
					<gift>base;wilderKey;1</gift>
					<gift>things;zodiacCash;1</gift>
					<gift>things;consLibra;2</gift>
					<gift>base;tenCoin;9</gift>
				</task>
				<task lv="80">
					<enemyLifeMul>20</enemyLifeMul><enemyDpsMul>10</enemyDpsMul>
					<gift>base;anniCoin;13</gift>
					<gift>base;wilderKey;1</gift>
					<gift>things;zodiacCash;1</gift>
					<gift>things;consLibra;2</gift>
					<gift>base;tenCoin;10</gift>
				</task>
				<task lv="90">
					<enemyLifeMul>20</enemyLifeMul><enemyDpsMul>10</enemyDpsMul>
					<gift>base;anniCoin;13</gift>
					<gift>base;wilderKey;2</gift>
					<gift>things;zodiacCash;1</gift>
					<gift>things;consLibra;2</gift>
					<gift>base;tenCoin;11</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>20</enemyLifeMul><enemyDpsMul>10</enemyDpsMul>
					<gift>base;anniCoin;14</gift>
					<gift>base;wilderKey;2</gift>
					<gift>things;zodiacCash;2</gift>
					<gift>things;consLibra;2</gift>
					<gift>base;tenCoin;11</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>80</enemyLifeMul><enemyDpsMul>20</enemyDpsMul>
					<gift>base;anniCoin;15</gift>
					<gift>base;wilderKey;2</gift>
					<gift>things;zodiacCash;2</gift>
					<gift>things;consLibra;2</gift>
					<gift>base;tenCoin;11</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>240</enemyLifeMul><enemyDpsMul>30</enemyDpsMul>
					<gift>base;anniCoin;15</gift>
					<gift>base;wilderKey;2</gift>
					<gift>things;zodiacCash;3</gift>
					<gift>things;consLibra;2</gift>
					<gift>base;tenCoin;12</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>600</enemyLifeMul><enemyDpsMul>40</enemyDpsMul>
					<gift>base;anniCoin;16</gift>
					<gift>base;wilderKey;2</gift>
					<gift>things;zodiacCash;3</gift>
					<gift>things;consLibra;2</gift>
					<gift>base;tenCoin;13</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>1800</enemyLifeMul><enemyDpsMul>50</enemyDpsMul>
					<gift>base;anniCoin;17</gift>
					<gift>base;wilderKey;2</gift>
					<gift>things;zodiacCash;3</gift>
					<gift>things;consLibra;2</gift>
					<gift>base;tenCoin;14</gift>
				</task>
			</growth>
				
		</task>
		
		
		
		<task name="newYearBlessing" cnName="新年祝福语" uiShowTime="999999" lv="99" unlockLv="1" levelIsTaskLvB="1">
			<openD start="2024-12-20" end="2025-1-1 23:59:59"/>
			<shortText>引爆超过200颗子弹绘制而成的祝福语。</shortText>
			<uiConditionText>引爆超过200颗子弹绘制而成的祝福语。</uiConditionText>
			<description>用荧光笔武器写下祝福语，至少用掉200颗弹药，并且引爆。按下副手键，可擦掉鼠标区域的子弹；按下装置键，可引爆在场所有子弹。</description>
			<!-- 地图 -->
			<worldMapId>XingTop</worldMapId>
			<levelId>activeTask/newYearBlessing</levelId>
			<diff>1</diff>
			<gift>base;wilderKey;2</gift>
			<gift>base;anniCoin;20</gift>
			<gift>base;tenCoin;10</gift>
			<gift>things;ghostStone;2</gift>
		</task>
		
		
		<task name="pumpkinBoss" cnName="南瓜怪" uiShowTime="999999" lv="99" unlockLv="1" levelIsTaskLvB="1">
			<openD start="2024-10-29" end="2024-11-11 23:59:59"/>
			<shortText>消耗南瓜怪的生命值</shortText>
			<uiConditionText>消耗南瓜怪的生命值</uiConditionText>
			<description>战斧高地突然降落了一只巨大的南瓜怪，它没有攻击能力，不受百分比伤害，且拥有化锁、胶性表皮技能。请尽全力消耗它的生命值，消耗的越多，限时结束后，获得的小南瓜就越多。</description>
			<!-- 地图 -->
			<worldMapId>MainUpland</worldMapId>
			<levelId>activeTask/pumpkinBoss</levelId>
			<condition time="80" timeType="win" timeFirst="倒计时" />
			<diff>1</diff>
			<gift>base;wilderKey;1</gift>
			<gift>base;pumpkin;8</gift>
		</task>
			
		<task name="killToSnakeTail" cnName="火种收集者" uiShowTime="999999" lv="99" unlockLv="30" moreKillEnemyNumIsMe="1" levelIsTaskLvB="1">
			<openD tk="PrisonFirst_wolf" start="2024-9-30" end="2024-10-7 23:59:59"/>
			<shortText>收集火种</shortText>
			<uiConditionText>收集火种</uiConditionText>
			<description>控制斩之使者击杀敌人，每击杀1个敌人，就能够收集它们的火种，但是火种越多，你受到的伤害也就越多。当你倒下时，或者通关时，系统将会根据收集的火种数量发放纪念币、十年币奖励。</description>
			<!-- 地图 -->
			<worldMapId>QingMing</worldMapId>
			<fixedLevelUrl>activeTask/killToSnakeTail</fixedLevelUrl>
			<diff>1</diff>
			<enemyLifeMul>0.8</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
			<gift>base;wilderKey;1</gift>
			<gift>things;longGlasses_1;3</gift>
			<gift>things;intensDrug;10</gift>
		</task>
	
		<task name="bossTask24_8" cnName="狂野的试炼" uiShowTime="999999" lv="99" unlockLv="30" moreKillEnemyNumIsMe="1" levelIsTaskLvB="1">
			<openD tk="PrisonFirst_wolf" start="2024-8-6" end="2024-8-31 23:59:59"/>
			<shortText>消灭守关首领</shortText>
			<uiConditionText>消灭守关首领</uiConditionText>
			<description>操作狂野收割者，击败守关首领。</description>
			<!-- 地图 -->
			<worldMapType>random</worldMapType>
			<fixedLevelUrl>activeTask/bossTask24_8</fixedLevelUrl>
			<diff>1</diff>
			<growth>
				<task>
					<enemyLifeMul>0.8</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<gift>base;anniCoin;15</gift>
					<gift>base;wilderKey;1</gift>
					<gift>things;zodiacCash;2</gift>
					<gift>things;sweepingCard;1</gift>
					<gift>base;tenCoin;8</gift>
				</task>
				<task>
					<enemyLifeMul>1.2</enemyLifeMul><enemyDpsMul>1.3</enemyDpsMul>
					<gift>base;anniCoin;18</gift>
					<gift>base;wilderKey;2</gift>
					<gift>things;zodiacCash;2</gift>
					<gift>things;sweepingCard;2</gift>
					<gift>base;tenCoin;9</gift>
				</task>
				<task>
					<enemyLifeMul>1.6</enemyLifeMul><enemyDpsMul>1.5</enemyDpsMul>
					<gift>base;anniCoin;21</gift>
					<gift>base;wilderKey;2</gift>
					<gift>things;zodiacCash;3</gift>
					<gift>things;sweepingCard;2</gift>
					<gift>base;tenCoin;10</gift>
				</task>
			</growth>
				
		</task>
		
		
		
		<task name="godVehicle" cnName="无敌载具" lv="50" unlockLv="30" uiShowTime="999999">
			<openD start="2024-6-14" end="2024-7-31 23:59:59"/>
			<conditionText>使用载具击毙所有僵尸。</conditionText>
			<uiConditionText>击毙所有僵尸</uiConditionText>
			<description>使用载具击毙所有僵尸，期间不能退出载具。任务完成时间越短，获得十年币的数量就越多（最少5个，最多40个）。注意，飞行载具在任务中会被减慢移动速度。</description>
			<!-- 目的地 -->
			<worldMapId>NanTang</worldMapId>
			<levelId>activeTask/godVehicle</levelId>
			<gift>things;zodiacCash;3</gift>
		</task>
		
		
		<task name="worldSnakeActive" cnName="氩星吞噬者" uiShowTime="999999" lv="99" unlockLv="0" moreKillEnemyNumIsMe="1" levelIsTaskLvB="1" tempLvB="1">
			<openD start="2024-4-29" end="2024-5-5" />
			<conditionText>击败氩星吞噬者</conditionText>
			<uiConditionText>击败氩星吞噬者</uiConditionText>
			<description>生活在氩星地下的巨型怪兽，在与氩王的决斗当中，被击瞎了左眼，从此以后臣服于氩王。今日，氩星吞噬者通过虫洞来到了地球，它将带来无尽的灾难，无论如何我们都要将其击退！</description>
			<!-- 地图 -->
			<worldMapId>JungleDeep</worldMapId>
			<levelId>activeTask/worldSnakeActive</levelId>
			<uiFleshCondition><one>bodyEvent:die; anyone</one></uiFleshCondition>

			<growth>
				<task lv="70">
					<enemyLifeMul>5</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<gift>base;anniCoin;12</gift>
					<gift>base;wilderKey;1</gift>
					<gift>things;zodiacCash;1</gift>
					<gift>base;tenCoin;6</gift>
				</task>
				<task lv="80">
					<enemyLifeMul>5</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<gift>base;anniCoin;13</gift>
					<gift>base;wilderKey;1</gift>
					<gift>things;zodiacCash;2</gift>
					<gift>base;tenCoin;8</gift>
				</task>
				<task lv="90">
					<enemyLifeMul>5</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<gift>base;anniCoin;15</gift>
					<gift>base;wilderKey;2</gift>
					<gift>things;zodiacCash;2</gift>
					<gift>base;tenCoin;10</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>5</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<gift>base;anniCoin;17</gift>
					<gift>base;wilderKey;2</gift>
					<gift>things;zodiacCash;3</gift>
					<gift>base;tenCoin;12</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>30</enemyLifeMul><enemyDpsMul>3</enemyDpsMul>
					<gift>base;anniCoin;19</gift>
					<gift>base;wilderKey;2</gift>
					<gift>things;zodiacCash;3</gift>
					<gift>things;demonSpreadCard;1</gift>
					<gift>base;tenCoin;14</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>100</enemyLifeMul><enemyDpsMul>5</enemyDpsMul>
					<gift>base;anniCoin;21</gift>
					<gift>base;wilderKey;3</gift>
					<gift>things;zodiacCash;4</gift>
					<gift>things;demonSpreadCard;1</gift>
					<gift>base;tenCoin;16</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>400</enemyLifeMul><enemyDpsMul>7</enemyDpsMul>
					<gift>base;anniCoin;23</gift>
					<gift>base;wilderKey;3</gift>
					<gift>things;zodiacCash;4</gift>
					<gift>things;demonSpreadCard;1</gift>
					<gift>base;tenCoin;18</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>1500</enemyLifeMul><enemyDpsMul>8</enemyDpsMul>
					<gift>base;anniCoin;25</gift>
					<gift>base;wilderKey;3</gift>
					<gift>things;zodiacCash;5</gift>
					<gift>things;demonSpreadCard;1</gift>
					<gift>base;tenCoin;20</gift>
				</task>
			</growth>
		</task>
	
	<father name="active" cnName="活动"  dayNum="1" buyNum="0" autoUnlockByLevelB="1">
		<task name="falconTestTask" cnName="黄金隼的试炼" uiShowTime="999999" lv="99" unlockLv="0" moreKillEnemyNumIsMe="1" levelIsTaskLvB="1">
			<openD start="2023-11-28" end="2023-12-4" />
			<conditionText>通过关卡</conditionText>
			<uiConditionText>通过关卡</uiConditionText>
			<description>驾驶黄金隼通关。使用陆空切换键，可切换黄金隼飞行状态。</description>
			<!-- 地图 -->
			<worldMapId>XiFeng</worldMapId>
			<levelId>activeTask/falconTestTask</levelId>
			<uiFleshCondition><one>bodyEvent:die; anyone</one></uiFleshCondition>

			<growth>
				<task>
					<enemyLifeMul>1</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<gift>base;anniCoin;12</gift>
					<gift>things;intensDrug;6</gift>
					<gift>things;sweepingCard;1</gift>
				</task>
				<task>
					<enemyLifeMul>1.5</enemyLifeMul><enemyDpsMul>1.3</enemyDpsMul>
					<gift>base;anniCoin;14</gift>
					<gift>things;intensDrug;7</gift>
					<gift>things;sweepingCard;2</gift>
				</task>
				<task>
					<enemyLifeMul>2</enemyLifeMul><enemyDpsMul>2</enemyDpsMul>
					<gift>base;anniCoin;16</gift>
					<gift>things;intensDrug;8</gift>
					<gift>things;sweepingCard;3</gift>
				</task>
			</growth>
		</task>
		
		<task name="bossTask23_10" cnName="贪吃蛇" uiShowTime="999999" lv="99" unlockLv="40" moreKillEnemyNumIsMe="1" levelIsTaskLvB="1">
			<openD tk="PrisonFirst_wolf" start="2023-9-26" end="2023-10-6 23:59:59"/>
			<shortText>消灭守关首领</shortText>
			<uiConditionText>消灭守关首领</uiConditionText>
			<description>操作蛇型采矿机，消灭所有敌人。</description>
			<!-- 地图 -->
			<worldMapType>bossTask22</worldMapType>
			<fixedLevelUrl>activeTask/bossTask23_10</fixedLevelUrl>
			<diff>1</diff>
			<enemyLifeMul>0.8</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
			<gift>base;anniCoin;12</gift>
			<gift>things;demBall;12</gift>
			<gift>things;zodiacCash;2</gift>
				
		</task>
		
		
		
		
		
		<task name="armsEdit23" cnName="神武驾临" uiShowTime="999999" lv="99" unlockLv="40" moreKillEnemyNumIsMe="1" levelIsTaskLvB="1">
			<openD tk="PrisonFirst_wolf" start="2023-6-26" end="2023-8-25 23:59:59"/>
			<shortText>使用自创武器通关</shortText>
			<uiConditionText>使用自创武器通关</uiConditionText>
			<description>在“首领工厂>创造武器”中创造一把武器，并设为主力武器，就可以在该任务中使用该武器。</description>
			<!-- 地图 -->
			<worldMapType>armsEdit23</worldMapType>
			<fixedLevelUrl>activeTask/armsEdit23</fixedLevelUrl>
			<diff>1</diff>
			<growth>
				<task>
					<enemyLifeMul>0.5</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<gift>things;zodiacCash;1</gift>
					<gift>things;yaStone;1</gift>
					<gift>things;partsChest78;1</gift>
					<gift>base;anniCoin;18</gift>
				</task>
				<task>
					<enemyLifeMul>1.5</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<gift>things;zodiacCash;2</gift>
					<gift>things;yaStone;1</gift>
					<gift>things;partsChest78;1</gift>
					<gift>base;anniCoin;20</gift>
				</task>
				<task>
					<enemyLifeMul>4</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<gift>things;zodiacCash;2</gift>
					<gift>things;yaStone;1</gift>
					<gift>things;partsChest78;2</gift>
					<gift>base;anniCoin;22</gift>
				</task>
			</growth>
				
		</task>
		
		
		
		
		
		
		
		
		
		<task name="bossTask22_10" cnName="决斗者历险" uiShowTime="999999" lv="99" unlockLv="40" moreKillEnemyNumIsMe="1" levelIsTaskLvB="1">
			<openD tk="PrisonFirst_wolf" start="2022-9-30" end="2022-10-7 23:59:59"/>
			<shortText>消灭守关首领</shortText>
			<uiConditionText>消灭守关首领</uiConditionText>
			<description>隐藏在秘境中的决斗者，是传说中的泰拳高手。请合理使用它的招数，击败守关首领。</description>
			<!-- 地图 -->
			<worldMapType>bossTask22</worldMapType>
			<fixedLevelUrl>activeTask/bossTask22_10</fixedLevelUrl>
			<diff>1</diff>
			<growth>
				<task>
					<enemyLifeMul>0.6</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<gift>base;anniCoin;10</gift>
					<gift>things;demStone;3</gift>
					<gift>things;bossSumCard;1</gift>
				</task>
				<task>
					<enemyLifeMul>1</enemyLifeMul><enemyDpsMul>1.3</enemyDpsMul>
					<gift>base;anniCoin;12</gift>
					<gift>things;demStone;4</gift>
					<gift>things;bossSumCard;2</gift>
				</task>
				<task>
					<enemyLifeMul>1.6</enemyLifeMul><enemyDpsMul>2</enemyDpsMul>
					<gift>base;anniCoin;14</gift>
					<gift>things;demStone;5</gift>
					<gift>things;bossSumCard;3</gift>
				</task>
			</growth>
				
		</task>
		
		
		<task name="sumBossTask" cnName="首领闯关" uiShowTime="999999" lv="99" unlockLv="40" moreKillEnemyNumIsMe="1" levelIsTaskLvB="1" tempLvB="1">
			<openD start="2022-6-27" end="2022-8-27 23:59:59" />
			<shortText>消灭所有敌人</shortText>
			<conditionText>剩余时间 [time]</conditionText>
			<uiConditionText>用自己的主力首领，在时间 [time] 内消灭所有敌人</uiConditionText>
			<description>在任务中，玩家将召唤自己的主力首领参加战斗（AI自动战斗）。</description>
			<!-- 地图 -->
			<worldMapType>random</worldMapType>
			<fixedLevelUrl>activeTask/sumBossTask</fixedLevelUrl>
			<diff>1</diff>
			<growth>
				<task lv="50">
					<enemyLifeMul>1</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<condition time="300" timeType="fail"  timeFirst="倒计时 "/>
					<gift>things;zodiacCash;1</gift>
					<gift>base;wilderKey;2</gift>
					<gift>base;anniCoin;10</gift>
					<gift>things;partsChest78;1</gift>
				</task>
				<task lv="80">
					<enemyLifeMul>1</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<condition time="300" timeType="fail"  timeFirst="倒计时 "/>
					<gift>things;zodiacCash;1</gift>
					<gift>base;wilderKey;2</gift>
					<gift>base;anniCoin;12</gift>
					<gift>things;partsChest78;1</gift>
				</task>
				<task lv="92">
					<enemyLifeMul>1</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<condition time="300" timeType="fail"  timeFirst="倒计时 "/>
					<gift>things;zodiacCash;2</gift>
					<gift>base;wilderKey;2</gift>
					<gift>base;anniCoin;12</gift>
					<gift>things;partsChest78;2</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>1</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<condition time="300" timeType="fail"  timeFirst="倒计时 "/>
					<gift>things;zodiacCash;2</gift>
					<gift>base;wilderKey;2</gift>
					<gift>base;anniCoin;14</gift>
					<gift>things;partsChest78;2</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>4</enemyLifeMul><enemyDpsMul>2</enemyDpsMul>
					<condition time="300" timeType="fail"  timeFirst="倒计时 "/>
					<gift>things;zodiacCash;2</gift>
					<gift>base;wilderKey;2</gift>
					<gift>base;anniCoin;16</gift>
					<gift>things;partsChest78;2</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>16</enemyLifeMul><enemyDpsMul>4</enemyDpsMul>
					<condition time="300" timeType="fail"  timeFirst="倒计时 "/>
					<gift>things;zodiacCash;2</gift>
					<gift>base;wilderKey;3</gift>
					<gift>base;anniCoin;16</gift>
					<gift>things;partsChest78;2</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>64</enemyLifeMul><enemyDpsMul>7</enemyDpsMul>
					<condition time="300" timeType="fail"  timeFirst="倒计时 "/>
					<gift>things;zodiacCash;2</gift>
					<gift>base;wilderKey;3</gift>
					<gift>base;anniCoin;18</gift>
					<gift>things;partsChest78;2</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>256</enemyLifeMul><enemyDpsMul>10</enemyDpsMul>
					<condition time="300" timeType="fail"  timeFirst="倒计时 "/>
					<gift>things;zodiacCash;2</gift>
					<gift>base;wilderKey;3</gift>
					<gift>base;anniCoin;18</gift>
					<gift>things;arenaChest;1</gift>
					<gift>things;partsChest78;2</gift>
				</task>
				<task lv="99">
					<enemyLifeMul>1000</enemyLifeMul><enemyDpsMul>15</enemyDpsMul>
					<condition time="300" timeType="fail"  timeFirst="倒计时 "/>
					<gift>things;zodiacCash;2</gift>
					<gift>base;wilderKey;4</gift>
					<gift>base;anniCoin;18</gift>
					<gift>things;arenaChest;1</gift>
					<gift>things;partsChest78;2</gift>
				</task>
			</growth>
				
		</task>
		
		<task name="activeWarrior" cnName="狂人的新品" uiShowTime="999999" lv="99" unlockLv="40" moreKillEnemyNumIsMe="1" levelIsTaskLvB="1">
			<openD start="2022-5-17" end="2022-5-17 23:59:59" />
			<shortText>消灭守关首领</shortText>
			<conditionText>剩余时间 [time]</conditionText>
			<uiConditionText>在时间 [time] 内消灭守关首领</uiConditionText>
			<description>狂人研制出了新的狂人机器，帮他测试一下这台新机甲的威力。</description>
			<!-- 地图 -->
			<worldMapType>random</worldMapType>
			<fixedLevelUrl>activeTask/activeWarrior</fixedLevelUrl>
			<diff>1</diff>
			<growth>
				<task>
					<enemyLifeMul>0.8</enemyLifeMul><enemyDpsMul>0.8</enemyDpsMul>
					<condition time="300" timeType="fail"  timeFirst="倒计时 "/>
					<gift>base;anniCoin;16</gift>
					<gift>things;yearMonkey;3</gift>
					<gift>things;workerHead;5</gift>
				</task>
				<task>
					<enemyLifeMul>1.5</enemyLifeMul><enemyDpsMul>1</enemyDpsMul>
					<condition time="300" timeType="fail" timeFirst="倒计时 "/>
					<gift>base;anniCoin;18</gift>
					<gift>things;yearMonkey;4</gift>
					<gift>things;workerHead;5</gift>
					<gift>things;rebirthStone;1</gift>
				</task>
				<task>
					<enemyLifeMul>2.2</enemyLifeMul><enemyDpsMul>1.3</enemyDpsMul>
					<condition time="300" timeType="fail" timeFirst="倒计时 "/>
					<gift>base;anniCoin;20</gift>
					<gift>things;yearMonkey;5</gift>
					<gift>things;workerHead;5</gift>
					<gift>things;rebirthStone;1</gift>
					<gift>things;skillFleshCard;1</gift>
				</task>
				<task>
					<enemyLifeMul>3</enemyLifeMul><enemyDpsMul>1.6</enemyDpsMul>
					<condition time="300" timeType="fail" timeFirst="倒计时 "/>
					<gift>base;anniCoin;20</gift>
					<gift>things;yearMonkey;6</gift>
					<gift>things;workerHead;5</gift>
					<gift>things;rebirthStone;1</gift>
					<gift>things;skillFleshCard;1</gift>
					<gift>things;teamRebirthCard;1</gift>
				</task>
			</growth>
				
		</task>
		
		
		
		
		
		
		
		<task name="highPet" cnName="神宠历险" uiShowTime="999999" lv="50" unlockLv="0" moreKillEnemyNumIsMe="1" levelIsTaskLvB="1" tempLvB="1">
			<openD start="2022-2-11" end="2022-2-28" />
			<conditionText>剩余时间 [time][n]消灭守关首领</conditionText>
			<uiConditionText>在时间 [time] 内消灭守关首领</uiConditionText>
			<description>任务中，玩家将控制自己出战的宠物挑战敌人。宠物会获得一定的攻击力、防御力加成（基础属性越低获得加成越高），并且对敌人的技能免疫。</description>
			<!-- 地图 -->
			<worldMapType>random</worldMapType>
			<fixedLevelUrl>activeTask/highPet</fixedLevelUrl>
			
			<growth>
				<task lv="70">
					<diff>2</diff>
					<condition type="collect" target="killEnemyNum" targetId="boss" value="1" time="420" timeType="fail"/>
					<gift>base;anniCoin;10</gift>
					<gift>things;zodiacCash;2</gift>
					<gift>things;partsChest78;1</gift>
				</task>
				<task lv="80">
					<diff>2</diff>
					<condition type="collect" target="killEnemyNum" targetId="boss" value="1" time="420" timeType="fail"/>
					<gift>base;anniCoin;12</gift>
					<gift>things;zodiacCash;2</gift>
					<gift>things;partsChest78;2</gift>
				</task>
				<task lv="90">
					<diff>2</diff>
					<condition type="collect" target="killEnemyNum" targetId="boss" value="1" time="420" timeType="fail"/>
					<gift>base;anniCoin;14</gift>
					<gift>things;zodiacCash;3</gift>
					<gift>things;partsChest78;2</gift>
				</task>
				<task lv="99">
					<diff>2</diff>
					<condition type="collect" target="killEnemyNum" targetId="boss" value="1" time="420" timeType="fail"/>
					<gift>base;anniCoin;16</gift>
					<gift>things;zodiacCash;3</gift>
					<gift>things;partsChest78;3</gift>
				</task>
				<task lv="99">
					<diff>5.5</diff>
					<condition type="collect" target="killEnemyNum" targetId="boss" value="1" time="420" timeType="fail"/>
					<gift>base;anniCoin;18</gift>
					<gift>things;zodiacCash;4</gift>
					<gift>things;partsChest78;3</gift>
				</task>
				<task lv="99">
					<diff>16</diff>
					<condition type="collect" target="killEnemyNum" targetId="boss" value="1" time="420" timeType="fail"/>
					<gift>base;anniCoin;20</gift>
					<gift>things;zodiacCash;4</gift>
					<gift>things;partsChest78;3</gift>
					<gift>things;arenaChest;1</gift>
				</task>
				<task lv="99">
					<diff>48</diff>
					<condition type="collect" target="killEnemyNum" targetId="boss" value="1" time="420" timeType="fail"/>
					<gift>base;anniCoin;25</gift>
					<gift>things;zodiacCash;4</gift>
					<gift>things;partsChest78;4</gift>
					<gift>things;arenaChest;1</gift>
				</task>
			</growth>
				
		</task>
		
		
		<task name="aniver22" cnName="超级散射" uiShowTime="999999" lv="70" unlockLv="0" moreKillEnemyNumIsMe="1" levelIsTaskLvB="1" tempLvB="1">
			<openD start="2022-1-13" end="2022-2-17" />
			<conditionText>剩余时间 [time][n]僵尸 [nowNum]/[num]</conditionText>
			<uiConditionText>在时间 [time] 内消灭[num] 个僵尸</uiConditionText>
			<description>在任务中，玩家的武器将获得“超级散射”效果，每种武器每周只能获得1次超级散射效果，任务中只使用一把武器是最佳的策略。</description>
			<!-- 地图 -->
			<worldMapId>YangMei</worldMapId>
			<levelId>aniver22</levelId>
			<noEnemyWhenCompleteB>1</noEnemyWhenCompleteB>
			<uiFleshCondition><one>bodyEvent:die; anyone</one></uiFleshCondition>
			<condition type="collect" target="killEnemyNum" value="1500" time="180" timeType="fail" cumulativeType="no" />
			<growth>
				<task lv="70">
					<diff>10</diff>
					<condition type="collect" target="killEnemyNum" value="1500" time="240" timeType="fail" cumulativeType="no" />
					<gift>things;pianoGun;1</gift>
					<gift>things;skyArch;1</gift>
				</task>
				<task lv="80">
					<diff>10</diff>
					<condition type="collect" target="killEnemyNum" value="1500" time="230" timeType="fail" cumulativeType="no" />
					<gift>things;pianoGun;2</gift>
					<gift>things;skyArch;1</gift>
				</task>
				<task lv="90">
					<diff>10</diff>
					<condition type="collect" target="killEnemyNum" value="1500" time="220" timeType="fail" cumulativeType="no" />
					<gift>things;pianoGun;2</gift>
					<gift>things;skyArch;2</gift>
				</task>
				<task lv="99">
					<diff>10</diff>
					<condition type="collect" target="killEnemyNum" value="1500" time="210" timeType="fail" cumulativeType="no" />
					<gift>things;pianoGun;2</gift>
					<gift>things;skyArch;2</gift>
					<gift>things;arenaChest;1</gift>
				</task>
				<task lv="99">
					<diff>40</diff>
					<condition type="collect" target="killEnemyNum" value="1500" time="200" timeType="fail" cumulativeType="no" />
					<gift>things;pianoGun;2</gift>
					<gift>things;skyArch;3</gift>
					<gift>things;arenaChest;1</gift>
				</task>
				<task lv="99">
					<diff>160</diff>
					<condition type="collect" target="killEnemyNum" value="1500" time="190" timeType="fail" cumulativeType="no" />
					<gift>things;pianoGun;2</gift>
					<gift>things;skyArch;3</gift>
					<gift>things;arenaChest;2</gift>
				</task>
				<task lv="99">
					<diff>600</diff>
					<condition type="collect" target="killEnemyNum" value="1500" time="180" timeType="fail" cumulativeType="no" />
					<gift>things;pianoGun;2</gift>
					<gift>things;skyArch;3</gift>
					<gift>things;arenaChest;3</gift>
				</task>
			</growth>
				
		</task>
		]]>
	</father>
	
</data>