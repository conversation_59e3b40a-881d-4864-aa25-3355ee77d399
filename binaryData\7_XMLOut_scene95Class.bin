<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="<PERSON><PERSON><PERSON>" cnName="寒光">
		<scene name="寒光1">
			<name>HanGuang1</name>
			<cnName>寒光入口</cnName>
			<swfUrl>swf/scene/HanGuang1.swf</swfUrl>
			<imgUrl>HanGuang1/s1</imgUrl>
			<sceneWidth>2900</sceneWidth>
			<sceneHeight>1205</sceneHeight>
			<heroStandardY>0</heroStandardY>
			<viewRangeRect>0,-100,2900,1205</viewRangeRect>	<!-- 视野范围，摄影机怎么移动都不会超过这个范围-->
			<layer>
				<imgName>front</imgName>				<!-- 层实例名称【必备】-->
				<moveRateX>1</moveRateX>			<!-- X轴移动倍率，如果是 0 则是铺满屏幕-->
				<moveRateY>1</moveRateY>			<!-- Y轴移动倍率，如果是 0 则是铺满屏幕-->
				<containerName>top</containerName>	<!-- 加载在游戏中哪一层，不填，默认就是back-->
			</layer>
			<layer>
				<imgName>floor</imgName>				<!-- 层实例名称【必备】-->
				<moveRateX>1</moveRateX>			<!-- X轴移动倍率，如果是 0 则是铺满屏幕-->
				<moveRateY>1</moveRateY>			<!-- Y轴移动倍率，如果是 0 则是铺满屏幕-->
			</layer>
			<layer>
				<imgName>back</imgName>				<!-- 层实例名称【必备】-->
				<width>1700</width>					<!-- 层本身的高宽，除了铺满，一般没用-->
				<height>700</height>
				<containerName>last</containerName>	<!-- 加载在游戏中哪一层，不填，默认就是back-->
				<moveRateX>0</moveRateX>			<!-- X轴移动倍率，如果是 0 则是铺满屏幕-->
				<moveRateY>0</moveRateY>			<!-- Y轴移动倍率，如果是 0 则是铺满屏幕-->
			</layer>
			<spiderData>CT8BCoEjAQ1qdW1wUmEJdHlwZQdhcnIDeANGA3kDRwVpZANIDWZhdGhlcgQABgEJBwEGBW42BgVuMQYHbjExBIgdBAAEhW4EAAYFbjAEAAEKAQU/4MFSOC1zZQYJanVtcAkHAQYHbjI3BhgGGgSGZgQABIZPBAAGFgQAAQoBBAAGAQkHAQYFbjMGB24yNAYUBIgnBAAEhBsEAAYFbjIEAAEKAQW/4MFSOC1zZQYcCQcBBgVuOAYUBiQEiUkEAASFCwQABiAEAAEKAQQABgEJBwEGBW41BgVuNwYFbjkEjRQEAASFcAQABgVuNAQAAQoBBb/gwVI4LXNlBhwJBwEGLAYHbjE1Bi4Ejj4EAASGUwQABigEAAEKAQQABgEJBQEGIAYaBIhlBAAEhUgEAAYUBAABCgEEAAYBCQUBBi4GJgSMDgQABIUgBAAGKgQAAQoBBAAGAQkFAQYqBiAEimkEAASEdQQABiYEAAEKAQQABgEJBQEGB24xMAYoBI1rBAAEhwcEAAYsBAABCgEEAAYBCQUBBgduMTQGLASMdgQABIdDBAAGMgQAAQoBBAAGAQkFAQYWBgduMTIEh0QEAASHDAQABhgEAAEKAQQABgEJBQEGGAYHbjEzBIhRBAAEh0MEAAY2BAABCgEEAAYBCQUBBjYGNASKHwQABIdHBAAGOAQAAQoBBAAGAQkFAQY4BjIEi2QEAASHQwQABjQEAAEKAQQABgEJBQEGKAYHbjE2BI9PBAAEhXsEAAYwBAABCgEEAAYBCQUBBjAGB24xNwSQbgQABIVyBAAGOgQAAQoBBAAGAQkFAQY6BgduMTgEkg0EAASFSwQABjwEAAEKAQQABgEJBQEGPAYHbjE5BJMCBAAEhQIEAAY+BAABCgEEAAYBCQUBBj4GB24yMASTfgQABIQ4BAAGQAQAAQoBBAAGAQkFAQZABgduMjEElRsEAASELgQABkIEAAEKAQQABgEJAwEGQgSWPwQABIQyBAAGRAQAAQoBBAAGAQkFAQYHbjI1BgduMjYEg0UEAASEHgQABgduMjIEAAEKAQQABgEJBQEGIgZGBIYEBAAEhBoEAAYHbjIzBAABCgEEAAYBCQUBBiQGTASHEQQABIQcBAAGIgQAAQoBBAAGAQkFAQZMBkoEhGIEAASEHAQABkYEAAEKAQQABgEJBQEGB24yOQYHbjI4BIJuBAAEhikEAAZIBAABCgEEAAYBCQUBBlAGFgSFTAQABIVwBAAGHgQAAQoBBAAGAQkFAQZIBh4Eg3wEAASFawQABlAEAAEKAQQABgEJBQEGB24zMAZIBIFkBAAEhnsEAAZOBAABCgEEAAYBCQMBBk4EUAQABIdABAAGUgQAAQ==</spiderData>
		</scene>
		<scene name="寒光2">
			<name>HanGuang2</name>
			<cnName>寒光西城</cnName>
			<swfUrl>swf/scene/HanGuang2.swf</swfUrl>
			<imgUrl>HanGuang2/s1</imgUrl>
			<sceneWidth>2900</sceneWidth>
			<sceneHeight>1222</sceneHeight>
			<heroStandardY>0</heroStandardY>
			<viewRangeRect>0,0,2900,1222</viewRangeRect>	<!-- 视野范围，摄影机怎么移动都不会超过这个范围-->
			<layer>
				<imgName>front</imgName>				<!-- 层实例名称【必备】-->
				<moveRateX>1</moveRateX>			<!-- X轴移动倍率，如果是 0 则是铺满屏幕-->
				<moveRateY>1</moveRateY>			<!-- Y轴移动倍率，如果是 0 则是铺满屏幕-->
				<containerName>top</containerName>	<!-- 加载在游戏中哪一层，不填，默认就是back-->
			</layer>
			<layer>
				<imgName>floor</imgName>				<!-- 层实例名称【必备】-->
				<moveRateX>1</moveRateX>			<!-- X轴移动倍率，如果是 0 则是铺满屏幕-->
				<moveRateY>1</moveRateY>			<!-- Y轴移动倍率，如果是 0 则是铺满屏幕-->
			</layer>
			<layer>
				<imgName>back</imgName>				<!-- 层实例名称【必备】-->
				<width>1571</width>					<!-- 层本身的高宽，除了铺满，一般没用-->
				<height>916</height>
				<containerName>last</containerName>	<!-- 加载在游戏中哪一层，不填，默认就是back-->
				<moveRateX>0</moveRateX>			<!-- X轴移动倍率，如果是 0 则是铺满屏幕-->
				<moveRateY>0</moveRateY>			<!-- Y轴移动倍率，如果是 0 则是铺满屏幕-->
			</layer>
			<spiderData>CU8BCoEjAQVpZA1mYXRoZXINanVtcFJhCXR5cGUDeANGA3kDRwNIB2FycgYFbjABBAAGAQQaBAAEiDEEAAQACQMBBgVuMQoBBhYBBAAGAQSBJgQABIgwBAAEAAkFAQYUBgduMTQKAQYFbjIBBT/gwVI4LXNlBglqdW1wBIkrBAAEhW0EAAQACQcBBgduMTgGBW4zBgduMjgKAQYgAQW/+SH7VEQtGAYcBIpiBAAEhRYEAAQACQcBBhoGB24xOQYiCgEGBW40AQQABgEEkQMEAASFcQQABAAJBQEGB24yMwYFbjUKAQYqAQW/4MFSOC1zZQYcBJIYBAAEhmEEAAQACQcBBiYGB24zNwYFbjYKAQYuAQU/4MFSOC1zZQYcBJEaBAAEhz4EAAQACQcBBgduMzMGKgYHbjMyCgEGBW43AQQABgEElT4EAASGYwQABAAJBQEGB24zOAYHbjM2CgEGBW44AQQABgEEhjEEAASFEAQABAAJBwEGBW45BgduMTcGB24xMAoBBjwBBb/gwVI4LXNlBhwEh0MEAASFdwQABAAJBwEGQAYeBjoKAQZAAQQABgEEhl8EAASGMQQABAAJBQEGB24xMQY8CgEGQgEEAAYBBIVsBAAEhmEEAAQACQUBBgduMTIGQAoBBkQBBAAGAQSEYgQABIciBAAEAAkFAQYHbjEzBkIKAQZGAQQABgEEgyQEAASHOwQABAAJBQEGGAZECgEGGAEEAAYBBIIeBAAEh2cEAAQACQUBBhYGRgoBBgduMTUBBAAGAQSCbwQABIUMBAAEAAkFAQYHbjE2BhYKAQZKAQQABgEEhBwEAASFCwQABAAJBQEGPgZICgEGPgEEAAYBBIUxBAAEhRAEAAQACQUBBjoGSgoBBh4BBAAGAQSILgQABIVuBAAEAAkFAQY8BhoKAQYkAQQABgEEi1oEAASETAQABAAJBQEGIAYHbjIwCgEGTAEEAAYBBIxjBAAEhCIEAAQACQUBBiQGB24yMQoBBk4BBAAGAQSOEAQABIQeBAAEAAkFAQZMBgduMjIKAQZQAQQABgEEjwYEAASEVQQABAAJBQEGTgYoCgEGKAEEAAYBBJADBAAEhSUEAAQACQUBBlAGJgoBBgduMjQBBAAGAQSGVAQABIgtBAAEAAkDAQYHbjI1CgEGVAEEAAYBBId0BAAEiDAEAAQACQUBBgduMjYGUgoBBlYBBAAGAQSJJAQABIgvBAAEAAkFAQYiBlQKAQYHbjI3AQQABgEEiyEEAASHLgQABAAJBQEGB24yOQYiCgEGIgEEAAYBBIoZBAAEh3QEAAQACQUBBlgGVgoBBloBBAAGAQSMSAQABIZhBAAEAAkFAQYHbjMwBlgKAQZcAQQABgEEjXAEAASGXAQABAAJBQEGB24zMQZaCgEGXgEEAAYBBI8ABAAEhxoEAAQACQUBBjIGXAoBBjIBBAAGAQSQBgQABIc1BAAEAAkFAQYuBl4KAQYwAQQABgEEkiUEAASIDQQABAAJBQEGB24zNAYuCgEGYAEEAAYBBJMsBAAEiDAEAAQACQUBBgduMzUGMAoBBmIBBAAGAQSUbAQABIgyBAAEAAkFAQY4BmAKAQY4AQQABgEElhMEAASINwQABAAJAwEGYgoBBiwBBAAGAQSTOgQABIZYBAAEAAkFAQYqBjYKAQY2AQQABgEElDAEAASGWgQABAAJBQEGLAY0</spiderData>
		</scene>
		
		
		<scene name="寒光3">
			<name>HanGuang3</name>
			<cnName>寒光中心</cnName>
			<swfUrl>swf/scene/HanGuang3.swf</swfUrl>
			<imgUrl>HanGuang3/s1</imgUrl>
			<sceneWidth>2900</sceneWidth>
			<sceneHeight>1205</sceneHeight>
			<heroStandardY>0</heroStandardY>
			<viewRangeRect>0,-100,2900,1205</viewRangeRect>	<!-- 视野范围，摄影机怎么移动都不会超过这个范围-->
			<layer>
				<imgName>floor</imgName>				<!-- 层实例名称【必备】-->
				<moveRateX>1</moveRateX>			<!-- X轴移动倍率，如果是 0 则是铺满屏幕-->
				<moveRateY>1</moveRateY>			<!-- Y轴移动倍率，如果是 0 则是铺满屏幕-->
			</layer>
			<layer>
				<imgName>back</imgName>				<!-- 层实例名称【必备】-->
				<width>1100</width>					<!-- 层本身的高宽，除了铺满，一般没用-->
				<height>800</height>
				<containerName>last</containerName>	<!-- 加载在游戏中哪一层，不填，默认就是back-->
				<moveRateX>0</moveRateX>			<!-- X轴移动倍率，如果是 0 则是铺满屏幕-->
				<moveRateY>0</moveRateY>			<!-- Y轴移动倍率，如果是 0 则是铺满屏幕-->
			</layer>
			<spiderData>CVcBCoEjAQVpZA1mYXRoZXINanVtcFJhCXR5cGUDeANGA3kDRwNIB2FycgYFbjABBAAGAQSGBAQABIVuBAAEAAkHAQYHbjI4BgVuMQYHbjEwCgEGGAEFP+DBUjgtc2UGCWp1bXAEhF8EAASGWgQABAAJBwEGB24zOAYaBhQKAQYFbjIBBAAGAQSJQwQABIQkBAAEAAkHAQYHbjIxBgVuMwYHbjExCgEGJAEFP+DBUjgtc2UGHASIPAQABIUIBAAEAAkHAQYgBiYGB24yNwoBBgVuNAEFP/kh+1RELRgGHASQHgQABIQaBAAEAAkHAQYFbjUGB24xOAYHbjEyCgEGLAEFv/kh+1RELRgGHASRTwQABIQeBAAEAAkHAQYHbjE3BioGMAoBBgVuNgEEAAYBBJAoBAAEhXgEAAQACQcBBgduMjYGBW43BjAKAQY4AQW/4MFSOC1zZQYcBJFfBAAEhkMEAAQACQcBBjAGB24xNQY0CgEGBW44AQW/4MFSOC1zZQYcBJY3BAAEhGwEAAQACQUBBgduMTMGBW45CgEGQAEEAAYBBJUKBAAEhCMEAAQACQcBBjwGB24xNgY+CgEGGgEEAAYBBIVABAAEhxsEAAQACQUBBhgGB24yOQoBBiYBBAAGAQSJDwQABIU/BAAEAAkFAQYkBgduMjIKAQYwAQQABgEEkHoEAASHCQQABAAJBQEGB24zNwY4CgEGPgEEAAYBBJVmBAAEhSMEAAQACQUBBgduNDIGPAoBBgduMTQBBAAGAQSUAgQABIVrBAAEAAkFAQY6BkoKAQY6AQQABgEEkmoEAASFcQQABAAJBQEGOAZMCgEGQgEEAAYBBJNnBAAEhB0EAAQACQUBBkAGMgoBBjIBBAAGAQSSTQQABIQdBAAEAAkFAQZCBiwKAQYuAQQABgEEjwMEAASEFwQABAAJBQEGKgYHbjE5CgEGTgEEAAYBBI1NBAAEhBUEAAQACQUBBi4GB24yMAoBBlABBAAGAQSMJQQABIQWBAAEAAkFAQZOBiIKAQYiAQQABgEEimYEAASEFAQABAAJBQEGUAYgCgEGRgEEAAYBBIoJBAAEhXEEAAQACQUBBiYGB24yMwoBBlIBBAAGAQSLFwQABIVuBAAEAAkFAQZGBgduMjQKAQZUAQQABgEEjDMEAASFbQQABAAJBQEGUgYHbjI1CgEGVgEEAAYBBI1OBAAEhWwEAAQACQUBBlQGNgoBBjYBBAAGAQSOfQQABIVuBAAEAAkFAQZWBjQKAQYoAQQABgEEhzoEAASFAwQABAAJBQEGJAYWCgEGFgEEAAYBBIZeBAAEhTMEAAQACQUBBigGFAoBBkQBBAAGAQSGJwQABIdGBAAEAAkFAQYaBgduMzAKAQZYAQQABgEEhzUEAASHSQQABAAJBQEGRAYHbjMxCgEGWgEEAAYBBIhNBAAEh0YEAAQACQUBBlgGB24zMgoBBlwBBAAGAQSJbwQABIdEBAAEAAkFAQZaBgduMzMKAQZeAQQABgEEixYEAASHQwQABAAJBQEGXAYHbjM0CgEGYAEEAAYBBIxABAAEh0IEAAQACQUBBl4GB24zNQoBBmIBBAAGAQSNRwQABIdCBAAEAAkFAQZgBgduMzYKAQZkAQQABgEEjkwEAASHPwQABAAJBQEGYgZICgEGSAEEAAYBBI90BAAEh0EEAAQACQUBBmQGMAoBBh4BBAAGAQSDOwQABIY+BAAEAAkFAQYHbjM5BhgKAQZmAQQABgEEgiYEAASGUQQABAAJBQEGB240MAYeCgEGaAEEAAYBBIE6BAAEhxYEAAQACQUBBgduNDEGZgoBBmoBBAAGAQRPBAAEh0cEAAQACQMBBmgKAQZKAQQABgEElQQEAASFWQQABAAJBQEGTAY+</spiderData>
		</scene>
		
		<scene name="寒光4">
			<name>HanGuang4</name>
			<cnName>寒光之巅</cnName>
			<swfUrl>swf/scene/HanGuang4.swf</swfUrl>
			<imgUrl>HanGuang4/s1</imgUrl>
			<sceneWidth>2900</sceneWidth>
			<sceneHeight>1205</sceneHeight>
			<heroStandardY>0</heroStandardY>
			<viewRangeRect>0,-100,2900,1205</viewRangeRect>	<!-- 视野范围，摄影机怎么移动都不会超过这个范围-->
			<layer>
				<imgName>front</imgName>				<!-- 层实例名称【必备】-->
				<moveRateX>1</moveRateX>			<!-- X轴移动倍率，如果是 0 则是铺满屏幕-->
				<moveRateY>1</moveRateY>			<!-- Y轴移动倍率，如果是 0 则是铺满屏幕-->
				<containerName>top</containerName>	<!-- 加载在游戏中哪一层，不填，默认就是back-->
			</layer>
			<layer>
				<imgName>floor</imgName>				<!-- 层实例名称【必备】-->
				<moveRateX>1</moveRateX>			<!-- X轴移动倍率，如果是 0 则是铺满屏幕-->
				<moveRateY>1</moveRateY>			<!-- Y轴移动倍率，如果是 0 则是铺满屏幕-->
			</layer>
			<layer>
				<imgName>back</imgName>				<!-- 层实例名称【必备】-->
				<width>1200</width>					<!-- 层本身的高宽，除了铺满，一般没用-->
				<height>800</height>
				<containerName>last</containerName>	<!-- 加载在游戏中哪一层，不填，默认就是back-->
				<moveRateX>0</moveRateX>			<!-- X轴移动倍率，如果是 0 则是铺满屏幕-->
				<moveRateY>0</moveRateY>			<!-- Y轴移动倍率，如果是 0 则是铺满屏幕-->
			</layer>
			<spiderData>CUUBCoEjAQVpZA1mYXRoZXINanVtcFJhCXR5cGUDeANGA3kDRwNIB2FycgYFbjABBAAGAQQDBAAEhz4EAAQACQMBBgVuMQoBBhYBBAAGAQRnBAAEhzYEAAQACQUBBhQGBW4yCgEGGAEEAAYBBIFoBAAEhnQEAAQACQUBBhYGBW4zCgEGGgEEAAYBBIJKBAAEhiwEAAQACQUBBhgGBW40CgEGHAEEAAYBBIM3BAAEhWoEAAQACQUBBhoGBW41CgEGHgEEAAYBBIQ4BAAEhWQEAAQACQUBBhwGBW42CgEGIAEEAAYBBIVGBAAEhWEEAAQACQUBBh4GBW43CgEGIgEEAAYBBIZjBAAEhWAEAAQACQUBBiAGBW44CgEGJAEEAAYBBId1BAAEhgYEAAQACQUBBiIGBW45CgEGJgEFP+DBUjgtc2UGCWp1bXAEiFIEAASGVgQABAAJBwEGJAYHbjEwBgduMzMKAQYqAQQABgEEiUsEAASHHQQABAAJBQEGJgYHbjExCgEGLgEEAAYBBIpNBAAEh0AEAAQACQUBBioGB24xOQoBBgduMTIBBAAGAQSOdQQABIVxBAAEAAkHAQYHbjMwBgduMTMGB24xNgoBBjYBBb/gwVI4LXNlBigEkCoEAASGSQQABAAJBwEGOAYHbjIwBjIKAQYHbjE0AQQABgEEjmkEAASEKAQABAAJBwEGB24yOQYHbjE1BjQKAQZAAQU/4MFSOC1zZQYoBI0kBAAEhHsEAAQACQcBBjwGB24zMQY0CgEGOAEEAAYBBI8xBAAEhwsEAAQACQUBBgduMTcGNgoBBkQBBAAGAQSOGAQABIdEBAAEAAkFAQYHbjE4BjgKAQZGAQQABgEEjFwEAASHQwQABAAJBQEGMAZECgEGMAEEAAYBBItNBAAEh0MEAAQACQUBBi4GRgoBBjoBBAAGAQSRKAQABIV8BAAEAAkFAQY2BgduMjEKAQZIAQQABgEEkl0EAASFbgQABAAJBQEGOgYHbjIyCgEGSgEEAAYBBJNvBAAEhg4EAAQACQUBBkgGB24yMwoBBkwBBAAGAQSUaAQABIZRBAAEAAkFAQZKBgduMjQKAQZOAQQABgEElV8EAASHHAQABAAJBQEGTAYHbjI1CgEGUAEEAAYBBJZNBAAEh0QEAAQACQMBBk4KAQYHbjI2AQQABgEEk0oEAASEHAQABAAJBQEGB24yOAZMCgEGB24yNwEEAAYBBJEHBAAEhBEEAAQACQUBBlQGPgoBBlQBBAAGAQSSMQQABIQVBAAEAAkFAQZSBlYKAQY+AQQABgEEj3IEAASEFAQABAAJBQEGVgY8CgEGNAEEAAYBBI4DBAAEhS8EAAQACQUBBkAGMgoBBkIBBAAGAQSMDAQABIR2BAAEAAkFAQZABgduMzIKAQZYAQQABgEEinMEAASFGAQABAAJBQEGQgYsCgEGLAEEAAYBBIl4BAAEhW4EAAQACQcBBlgGJgYq</spiderData>
		</scene>
		<scene name="寒光5">
			<name>HanGuang5</name>
			<cnName>寒光末路</cnName>
			<swfUrl>swf/scene/HanGuang5.swf</swfUrl>
			<imgUrl>HanGuang5/s1</imgUrl>
			<sceneWidth>2900</sceneWidth>
			<sceneHeight>1105</sceneHeight>
			<heroStandardY>0</heroStandardY>
			<viewRangeRect>0,0,2902,1406</viewRangeRect>	<!-- 视野范围，摄影机怎么移动都不会超过这个范围-->
			<layer>
				<imgName>floor</imgName>				<!-- 层实例名称【必备】-->
				<moveRateX>1</moveRateX>			<!-- X轴移动倍率，如果是 0 则是铺满屏幕-->
				<moveRateY>1</moveRateY>			<!-- Y轴移动倍率，如果是 0 则是铺满屏幕-->
			</layer>
			<layer>
				<imgName>back</imgName>				<!-- 层实例名称【必备】-->
				<width>1100</width>					<!-- 层本身的高宽，除了铺满，一般没用-->
				<height>800</height>
				<containerName>last</containerName>	<!-- 加载在游戏中哪一层，不填，默认就是back-->
				<moveRateX>0</moveRateX>			<!-- X轴移动倍率，如果是 0 则是铺满屏幕-->
				<moveRateY>0</moveRateY>			<!-- Y轴移动倍率，如果是 0 则是铺满屏幕-->
			</layer>
			<spiderData>CUMBCoEjAQVpZA1mYXRoZXINanVtcFJhCXR5cGUDeANGA3kDRwNIB2FycgYFbjABBAAGAQSDMAQABIZuBAAEAAkFAQYHbjIyBgVuMQoBBhgBBT/gwVI4LXNlBglqdW1wBIF+BAAEhz0EAAQACQcBBgduMjAGB24xNwYUCgEGBW4yAQQABgEEkngEAASGcAQABAAJBQEGBW4zBgduMzIKAQYiAQW/4MFSOC1zZQYaBJQ8BAAEhzgEAAQACQcBBgVuNAYFbjYGIAoBBiYBBAAGAQSTQAQABIgFBAAEAAkFAQYFbjcGIgoBBgVuNQEEAAYBBJY0BAAEhlMEAAQACQMBBigKAQYoAQQABgEElSoEAASGaQQABAAJBQEGIgYsCgEGKgEEAAYBBJJGBAAEiEUEAAQACQUBBgVuOAYmCgEGLgEEAAYBBJFNBAAEiQ4EAAQACQUBBgVuOQYqCgEGMAEEAAYBBJA2BAAEiWcEAAQACQUBBgduMTAGLgoBBjIBBAAGAQSObwQABIlwBAAEAAkFAQYHbjExBjAKAQY0AQQABgEEjS0EAASJbAQABAAJBQEGB24xMgYyCgEGNgEEAAYBBItoBAAEiWoEAAQACQUBBgduMTMGNAoBBjgBBAAGAQSKIwQABIlpBAAEAAkFAQYHbjE0BjYKAQY6AQQABgEEiGIEAASJbgQABAAJBQEGB24xNQY4CgEGPAEEAAYBBIciBAAEiWkEAAQACQUBBgduMTYGOgoBBj4BBAAGAQSFbgQABIlgBAAEAAkFAQYHbjE5BjwKAQYeAQQABgEEgnUEAASHfwQABAAJBQEGGAYHbjE4CgEGQgEEAAYBBINsBAAEiEMEAAQACQUBBh4GQAoBBkABBAAGAQSEaQQABIkPBAAEAAkFAQZCBj4KAQYcAQQABgEEgQoEAASGbgQABAAJBQEGB24yMQYYCgEGRAEEAAYBBBwEAASGUQQABAAJAwEGHAoBBhYBBAAGAQSEOAQABIYWBAAEAAkFAQYHbjIzBhQKAQZGAQQABgEEhT4EAASFSgQABAAJBQEGB24yNAYWCgEGSAEEAAYBBIZbBAAEhRsEAAQACQUBBgduMjUGRgoBBkoBBAAGAQSHdgQABIUfBAAEAAkFAQYHbjI2BkgKAQZMAQQABgEEiT0EAASFIAQABAAJBQEGB24yNwZKCgEGTgEEAAYBBIpzBAAEhSIEAAQACQUBBgduMjgGTAoBBlABBAAGAQSMIQQABIUjBAAEAAkFAQYHbjI5Bk4KAQZSAQQABgEEjWQEAASFIgQABAAJBQEGB24zMAZQCgEGVAEEAAYBBI8XBAAEhSAEAAQACQUBBgduMzEGUgoBBlYBBAAGAQSQTAQABIUxBAAEAAkFAQYkBlQKAQYkAQQABgEEkVwEAASGAwQABAAJBQEGIAZW</spiderData>
		</scene>
		
		<scene>
			<name>HanGuangSub</name>
			<cnName>寒光郊外</cnName>
			<sceneWidth>950</sceneWidth>
			<sceneHeight>600</sceneHeight>
			<viewRangeRect>0,0,950,600</viewRangeRect>
			<layer>
				<imgName>front</imgName>							
				<moveRateX>1</moveRateX>			
				<moveRateY>1</moveRateY>			
				<containerName>say</containerName>
			</layer>
			<layer>
				<imgName>floor</imgName>				
				<width>1218</width>					
				<height>600</height>
				<containerName>last</containerName>
				<moveRateX>1</moveRateX>			
				<moveRateY>1</moveRateY>			
			</layer>
			<layer>
				<imgName>back5</imgName>				
				<width>1218</width>					
				<height>600</height>
				<containerName>last</containerName>
				<moveRateX>0.3</moveRateX>			
				<moveRateY>1</moveRateY>	
			</layer>
			<layer>
				<imgName>back4</imgName>				
				<width>1218</width>					
				<height>600</height>
				<containerName>last</containerName>
				<moveRateX>0.2</moveRateX>			
				<moveRateY>1</moveRateY>	
			</layer>
			<layer>
				<imgName>back3</imgName>				
				<width>1218</width>					
				<height>600</height>
				<containerName>last</containerName>
				<moveRateX>0.1</moveRateX>			
				<moveRateY>1</moveRateY>		
			</layer>
			<layer>
				<imgName>back2</imgName>				
				<width>1218</width>					
				<height>600</height>
				<containerName>last</containerName>
				<moveRateX>0.05</moveRateX>			
				<moveRateY>1</moveRateY>		
			</layer>
			<layer>
				<imgName>back</imgName>				
				<width>1218</width>					
				<height>600</height>
				<containerName>last</containerName>
				<moveRateX>0.01</moveRateX>			
				<moveRateY>1</moveRateY>			
			</layer>
			<spiderData>CRcBCoEjAQVpZAN4A0YDeQNHA0gHYXJyDWZhdGhlcgl0eXBlDWp1bXBSYQYFbjAEFwQABIUMBAAEAAkDAQYFbjEBBgEEAAoBBhYEgQcEAASFDQQABAAJBQEGFAYFbjIBBgEEAAoBBhgEgXYEAASFCwQABAAJBQEGFgYFbjMBBgEEAAoBBhoEgmQEAASFDQQABAAJBQEGGAYFbjQBBgEEAAoBBhwEg1oEAASFCwQABAAJBQEGGgYFbjUBBgEEAAoBBh4EhEoEAASFBgQABAAJBQEGHAYFbjYBBgEEAAoBBiAEhSMEAASFBgQABAAJBQEGHgYFbjcBBgEEAAoBBiIEhg4EAASFBwQABAAJBQEGIAYFbjgBBgEEAAoBBiQEhwkEAASFCgQABAAJBQEGIgYFbjkBBgEEAAoBBiYEh3sEAASFDAQABAAJBQEGJAYHbjEwAQYBBAAKAQYoBIh7BAAEhQ0EAAQACQMBBiYBBgEEAA==</spiderData>
		</scene>
	</father>
		
</data>