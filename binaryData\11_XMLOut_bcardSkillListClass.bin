<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="bossEdit">
		<body name="bcardSkill" cnName="随机技能" noEncodeB="1">
			11_1:demCloned
11_2:summoned<PERSON>pider_spiderKing_extra
12_1:through_hero_6
12_2:through_enemy


12_5:crazy_enemy
12_6:groupCrazy_enemy
12_7:poisonClaw_wind
12_8:selfBurn_enemy
12_9:globalSpurting_enemy
12_10:trueshot_enemy

12_12:atry_skeleton
12_13:murderous_equip
12_14:skillGift_enemy

13_1:madCloseLightning
13_2:acidRain_SpiderKing
13_3:screwBall
13_4:lightningFloor
13_5:meteorite<PERSON><PERSON>
13_6:revengeArrow
13_7:revengeGhost


13_10:fightBackBullet
13_11:midLightning

14_1:metalCorrosion
14_2:BallLightningHurt
14_3:kill<PERSON>harm
14_4:killPet


21_1:silence_enemy

21_3:corpsePoison
21_4:extendCd


21_7:enemyEmp
21_8:invincibleEmp
21_9:summonShortLife
21_10:clawLing_10



22_1:electricBoom_enemy
22_2:paralysis_enemy
22_3:screaming_enemy
22_4:slowMoveHalo_enemy
22_5:slowMove_enemy
22_6:LastdayAir
22_7:hammer_enemy
22_8:posion7_wolf
22_9:toLand
22_10:anionSkin_equip





23_2:corrosion_enemy
23_3:magneticField_enemy
23_4:disabled_enemy



23_8:desertedHalo_enemy
23_9:blindness_skeleton
23_10:disabledHalo_enemy



31_1:FoggyDefence
31_2:feedback_enemy
31_3:ironBody_enemy
31_5:godHand_equip


31_6:refraction_equip
31_7:poisonRange_equip
31_8:pioneerDemon
31_9:strong_enemy
31_10:resistMulHurt
31_11:noDegradation

31_13:godShield




32_2:recovery_enemy

32_4:reverseHurt_enemy
32_5:treater_FightWolf
32_6:sacrifice_equip
32_7:fleshFeast_pig






















34_3:fightReduct
34_7:ruleRange
34_8:likeMissle_Shapers
34_9:likeMissleNo
34_10:noBounce_enemy
34_11:rigidBody_enemy

34_13:cmldef3_enemy
34_14:noUnderLaser


35_2:immune
35_3:treater_knights
35_4:vertigoArmorIron
35_5:KingRabbitTreater
35_6:strongLing_10





41_1:State_AddMove
41_2:State_AddMove50
41_3:groupSpeedUp_enemy
41_4:teleport_enemy
41_5:fastForward_enemy


42_1:hiding_enemy
42_2:hidingAll_enemy


42_5:findHide






		</body>
	</father>
		
</data>