<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="snake" cnName="血蟒">
		<body index="0" name="血蟒雇佣兵" shell="normal">
			
			<name>PythonEmploy</name><movieLink>XiaoHu</movieLink>
			<cnName>血蟒雇佣兵</cnName>
			<raceType>human</raceType>
			<swfUrl>swf/hero/PythonEmploy.swf</swfUrl>
			<!-- 基本系数 -->
			<showLevel>9999</showLevel>
			<headIconUrl>IconGather/PythonEmploy</headIconUrl>
			<lifeBarExtraHeight>-20</lifeBarExtraHeight>
			<!-- 碰撞体积 -->
			<hitRect>-12, -90, 24, 90</hitRect><!-- 站立碰撞体积-->
			<squatHitRect>-12,-50,24,50</squatHitRect><!-- 下蹲碰撞体积-->
			<hurtRectArr>-15,-38,30,38</hurtRectArr> <!-- 要害受伤体积-->
			<hurtRectArr>-12, -100, 24, 95</hurtRectArr> <!-- 站立受伤体积-->
			<hurtRectArr>-12, -50, 24, 50</hurtRectArr> <!-- 下蹲受伤体积-->
			<!-- 运动 -->
			<maxVx>10</maxVx>
			<squatMaxVx>5</squatMaxVx>
			<maxJumpNum>1</maxJumpNum>
			<flyType>space</flyType><motionD F_AIR="6" />
			<!-- AI属性 -->
			<armsNumber>1</armsNumber><!-- 武器个数 -->
			<randomArmsRange>pistol2</randomArmsRange>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr>imploding_enemy,moreMissile_enemy</bossSkillArr>
		</body>
	</father>
</data>