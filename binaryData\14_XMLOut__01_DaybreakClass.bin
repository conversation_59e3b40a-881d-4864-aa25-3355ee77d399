<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="car" cnName="战车定义">
		<equip name="Daybreak" cnName="破晓" evolutionLabel="MoonLack">
			<canComposeB>1</canComposeB>
			<main dpsMul="0.85" len="84"/>
			<sub dpsMul="1.3" len="40" />
			<mainFrontB>1</mainFrontB>
			<lifeMul>1</lifeMul>
			<attackMul>0.9</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>120</mustCash>
			<addObjJson>{'dpsAll':0.05,'lifeAll':0.05}</addObjJson>
		</equip>
		<equip name="MoonLack" cnName="月蚀" evolutionLabel="Dayspring">
			<evolutionLv>2</evolutionLv>
			<main label="Daybreak_main" dpsMul="1.1" len="84"/>
			<sub label="Daybreak_sub" dpsMul="1.7" len="40" />
			<mainFrontB>1</mainFrontB>
			<lifeMul>1</lifeMul>
			<attackMul>1</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>120</mustCash>
			<addObjJson>{'dpsAll':0.08,'lifeAll':0.08}</addObjJson>
			<skillArr>vehicleFit_Civilian</skillArr>
		</equip>
		<equip name="Dayspring" cnName="黎明" evolutionLabel="Daylight">
			<evolutionLv>4</evolutionLv>
			<main label="Daybreak_main" dpsMul="1.3" len="84"/>
			<sub label="Daybreak_sub" dpsMul="2" len="40" />
			<mainFrontB>1</mainFrontB>
			<lifeMul>1.5</lifeMul>
			<attackMul>1.3</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>120</mustCash>
			<addObjJson>{'dpsAll':0.10,'lifeAll':0.10}</addObjJson>
			<skillArr>vehicleFit_Civilian</skillArr>
		</equip>
		<equip name="Daylight" cnName="曙光">
			<evolutionLv>5</evolutionLv>
			<main label="Daybreak_main" dpsMul="1.7" len="88"/>
			<sub label="Daybreak_sub" dpsMul="2.3" len="42" />
			<mainFrontB>1</mainFrontB>
			<lifeMul>2.3</lifeMul>
			<attackMul>1.7</attackMul>
			<duration>40</duration>
			<cd>120</cd>
			<mustCash>120</mustCash>
			<addObjJson>{'dpsAll':0.16,'lifeAll':0.16}</addObjJson>
			<skillArr>daylightSkill,vehicleFit_Civilian</skillArr>
		</equip>
		
		<bullet cnName="破晓-主炮">
			<name>Daybreak_main</name>
			<cnName>破晓-主炮</cnName>
			<!--武器属性------------------------------------------------------------ -->
			<hurtRatio>0.93</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>1</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.7</attackGap>
			
			<!--运动属性------------------------------------------------------------ -->	
			<shootRecoil>15</shootRecoil>
			<screenShakeValue>16</screenShakeValue>
			<bulletSpeed>45</bulletSpeed>
			<boomD  bodyB="1" floorB="1" radius="120"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">rocket/rocketBullet</bulletImgUrl>
			<fireImgUrl raNum="30" soundUrl="rocket/barrel2_sound" con="add">gunFire/rocket</fireImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom1"  shake="2,0.2,10">boomEffect/boom3</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="破晓-副炮">
			<name>Daybreak_sub</name>
			<cnName>破晓-副炮</cnName>
			<!--武器属性------------------------------------------------------------ -->
			<hurtRatio>0.66</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>0.001</bulletLife>
			<hitType>longLine</hitType>
			<bulletWidth>800</bulletWidth>
			<bulletShakeWidth>100</bulletShakeWidth>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.1</attackGap>
			<attackDelay>0</attackDelay>
			<!--运动属性------------------------------------------------------------ -->	
			<shootRecoil>4</shootRecoil>
			<screenShakeValue>9</screenShakeValue>
			<shakeAngle>1</shakeAngle>
			<bulletSpeed>0</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<lineD lightColor="0xFFFF00" size="2" lightSize="6"/>
			<bulletImgUrl>longLine</bulletImgUrl>
			<fireImgUrl raNum="30" soundUrl="ak/barrel2_sound">gunFire/f</fireImgUrl>
			<hitImgUrl>bulletHitEffect/yellow_motion</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
	</father>
	
	
	<father name="vehicleSkill" cnName="技能">
		<skill>
			<name>daylightSkill</name><noBeClearB>1</noBeClearB>
			<cnName>裂火钻</cnName><iconUrl>SkillIcon/through_hero</iconUrl>
			<effectInfoArr>增加伤害输出</effectInfoArr>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>70</cd>
			<changeText>持续[duration]秒</changeText>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>beforeAttack</condition><!-- 在准备攻击之前触发 -->
			<target>me</target>
			<passiveSkillArr>daylightSkill_link</passiveSkillArr>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>daylightSkill</effectType><effectFather>vehicle</effectFather>
			<value>2.5</value>
			<secMul>3</secMul>
			<duration>1.5</duration>
			
			<!--图像------------------------------------------------------------ -->
			<meEffectImg name="through_hero_me"/>
			<stateEffectImg name="burstParts"/>
			<description>机枪开启无限穿人穿墙模式，伤害增至[value]倍（对火焰敏感的敌人再提升[secMul]的伤害），持续[duration]秒。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><duration>1.5</duration></skill>
				<skill><duration>1.8</duration></skill>
				<skill><duration>2.1</duration></skill>
				<skill><duration>2.4</duration></skill>
				<skill><duration>2.7</duration></skill>
				<skill><duration>3</duration></skill>
				<skill><duration>3.3</duration></skill>
				<skill><duration>3.6</duration></skill>
				<skill><duration>4</duration></skill>
				<skill><duration>5</duration></skill>
			</growth>
		</skill>
				<skill>
					<name>daylightSkill_link</name>
					<cnName>对火焰敏感提升</cnName><noRandomListB>1</noRandomListB><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
					<!--触发条件与目标------------------------------------------------------------ -->
					<conditionType>passive</conditionType><changeHurtB>1</changeHurtB>
					<condition>hit</condition>
					<conditionString>compound</conditionString><!-- 复合外壳-火焰敏感 -->
					<target>target</target>
					<!--效果------------------------------------------------------------ -->
					<addType>instant</addType>
					<effectType>vehicleSkillEle</effectType><effectFather>vehicle</effectFather>
					<valueString>Daybreak_sub</valueString><!-- 指定子弹名字，attack就是碰撞伤害，不指定就是全部伤害 -->
					<mul>3</mul>
					<pointEffectImg name="orangeWaveBoom"/>
				</skill>
	</father>
	
	
	
	<father name="vehicle" cnName="战车body">
		<body index="0" name="破晓" shell="metal">
			
			<name>Daybreak</name>
			<cnName>破晓</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/vehicle/Daybreak38.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>0.5</headHurtMul>
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgClass>CarImage</imgClass>
			<imgType>normal</imgType>
			<rotateBySlopeB>1</rotateBySlopeB>
			<imgArr>
				stand,move,die1
				,__jumpUp,jumpUp,jumpDown,jumpDown__
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<motionD F_G="0.8" jumpDelayT="0.15" F_I="0.3" F_F="0.9" moveWhenVB="1" />
			<maxVx>18</maxVx>
			<maxJumpNum>1</maxJumpNum>
			<!-- 技能 -->
			<attackAIClass>CarAttack_AI</attackAIClass>
			<keyClass>CarBodyKey</keyClass>
			<bulletLauncherClass>CarBulletLauncher</bulletLauncherClass>
			<skillArr></skillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>move</imgLabel>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit4">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpDown</imgLabel>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpUp</imgLabel>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpDown__</imgLabel>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>
		
		<body name="月蚀" fixed="Daybreak" shell="metal">
			<name>MoonLack</name>
			<cnName>月蚀</cnName>
			<swfUrl>swf/vehicle/MoonLack.swf</swfUrl>
			<bmpUrl>BodyImg/MoonLack</bmpUrl>
		</body>	
		<body name="黎明" fixed="Daybreak" shell="metal">
			<name>Dayspring</name>
			<cnName>黎明</cnName>
			<swfUrl>swf/vehicle/Dayspring.swf</swfUrl>
			<bmpUrl>BodyImg/Dayspring</bmpUrl>
		</body>	
		<body name="曙光" fixed="Daybreak" shell="metal">
			<name>Daylight</name>
			<cnName>黎明</cnName>
			<swfUrl>swf/vehicle/Daylight.swf</swfUrl>
			<bmpUrl>BodyImg/Daylight</bmpUrl>
			<motionD F_G="0.8" jumpDelayT="0.15" F_I="0.7" F_F="0.9" moveWhenVB="1" />
		</body>	
	</father>
</data>