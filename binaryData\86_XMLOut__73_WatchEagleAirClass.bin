<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="aircraft" cnName="战车定义">
		<equip name="WatchEagleAir" cnName="守望之翼" rideLabel="RedMotoRide"  defineType="normal">
			<canComposeB>1</canComposeB>
			<mustCash>80</mustCash>
			<lifeMul>1.6</lifeMul>
			<attackMul>2.3</attackMul><attackActionLabel>sprintAttack</attackActionLabel>
			<duration>40</duration>
			<cd>120</cd>
			<addObjJson>{'dpsAll':0.08,'lifeAll':0.08}</addObjJson>
			<skillArr>murderous_vehicle,WatchEagleAirDefence,vehicleFit_fly</skillArr>
		</equip>
	</father>
	
	
	
	
	
	
	<father name="vehicle" cnName="战车body">
		<body index="0" name="守望之翼" shell="metal">
			
			<name>WatchEagleAir</name>
			<cnName>守望之翼</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/vehicle/WatchEagleAir176.swf</swfUrl>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<flipCtrlBy>mouse</flipCtrlBy>
			<dieJumpMul>0</dieJumpMul>
			<showLevel>999</showLevel>
			<imgArr>
				stand,move,hurt1,die1
				,sprintAttack
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-25,-15,50,30</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>15</maxVx>
			<motionState>fly</motionState><flyUseSpiderB>1</flyUseSpiderB><flyType>space</flyType>
			<motionD F_AIR="3" />
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>WatchEagleAir_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<bulletLauncherClass>VehicleBulletLauncher</bulletLauncherClass>
			<keyClass>VehicleBodyKey</keyClass>
			<bossSkillArrCn></bossSkillArrCn>
			<!-- 攻击数据 -->
			<hurtArr>
				<![CDATA[
				<hurt>
					<imgLabel>shootAttack</imgLabel>
					<bulletLabel>WatchEagleAir_shoot</bulletLabel>
					<grapRect>-400,-50,450,200</grapRect>
					<hurtRatio>0.001</hurtRatio>
				</hurt>
				
				<hurt  info="不加入ai选择"><noAiChooseB>1</noAiChooseB>
					<imgLabel>sprintAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtMul>0.05</hurtMul>
					<grapRect>-73,150,146,200</grapRect>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl soundUrl="sound/body_hit">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				]]>
				<hurt  info="不加入ai选择">
					<imgLabel>sprintAttack</imgLabel><meBack>40</meBack>
					<grapRect>-87,61,146,262</grapRect><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>2</hurtRatio>
					<attackType>through</attackType>
					<skillArr></skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl soundUrl="sound/body_hit">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				
			</hurtArr>
		</body>
	</father>
</data>