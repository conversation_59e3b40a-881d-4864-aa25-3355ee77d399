<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="bossEdit">
		<body name="bossEditSkill" cnName="可选技能" noEncodeB="1">
			11_1:demCloned
11_2:summoned<PERSON><PERSON>er_spiderKing_extra
11_3:changeToGaia
11_4:changeToSaberTiger
11_5:changeToThunderbolt
11_6:changeToSecDig<PERSON>
11_7:OreWormAI_sum
11_8:boundless_enemy
11_9:demCloned2
11_10:summSentry
11_11:DuelistSummoned


12_1:through_hero_6
12_2:through_enemy
12_3:moreBullet
12_4:MeatyAway
12_5:crazy_enemy
12_6:groupCrazy_enemy
12_7:poison<PERSON>law_wind
12_8:selfBurn_enemy
12_9:globalSpurting_enemy
12_10:trueshot_enemy
12_11:missile_Sentry
12_12:atry_skeleton
12_13:murderous_equip
12_14:skillGift_enemy
12_15:fastCd

13_1:mad<PERSON>lose<PERSON>ightning
13_2:acidRain_SpiderKing
13_3:screwBall
13_4:lightning<PERSON><PERSON>or
13_5:meteorite<PERSON><PERSON>
13_6:revenge<PERSON>rrow
13_7:revengeGhost
13_8:deadlyArrow
13_9:deadlyGhost
13_10:fightBackBullet
13_11:midLightning
13_12:crazy_vehicle_10
13_13:floorTankSkill_10

14_1:metalCorrosion
14_2:BallLightningHurt
14_3:killCharm
14_4:killPet
14_5:killXinLing
14_6:killAllSummon

15_1:addMove_Crawler
15_2:splash_HugePoison



21_1:silence_enemy
21_2:offAllSkill
21_3:corpsePoison
21_4:extendCd
21_5:MeatySkillBack
21_6:skillCopyTransport
21_7:enemyEmp
21_8:invincibleEmp
21_9:summonShortLife
21_10:clawLing_10
21_11:everSilenceEnemy
21_12:everNoSkillEnemy
21_13:summonShortLifeMax
21_14:meltFlamerPurgold

22_1:electricBoom_enemy
22_2:paralysis_enemy
22_3:screaming_enemy
22_4:slowMoveHalo_enemy
22_5:slowMove_enemy
22_6:LastdayAir
22_7:hammer_enemy
22_8:posion7_wolf
22_9:toLand
22_10:anionSkin_equip
22_11:enemyToZombie
22_12:enemyToSpider
22_13:cantMove
22_14:enemyToMe
22_15:vacuumJie_11


23_1:LastdayHit
23_2:corrosion_enemy
23_3:magneticField_enemy
23_4:disabled_enemy
23_5:corrosion_hugePosion
23_6:clearAttackIron
23_7:shortLivedDisabled
23_8:desertedHalo_enemy
23_9:blindness_skeleton
23_10:disabledHalo_enemy
23_11:noPurgoldArms
23_12:shortShootRangeHole
23_13:blackHoleDemon
23_14:noRocket
23_15:acidicBlood


31_1:FoggyDefence
31_2:feedback_enemy
31_3:ironBody_enemy
31_4:WarriorShield
31_5:godHand_equip
31_6:refraction_equip
31_7:poisonRange_equip
31_8:pioneerDemon
31_9:strong_enemy
31_10:resistMulHurt
31_11:noDegradation
31_12:verShield
31_13:godShield
31_14:invincibleHole
31_15:State_Invincible
31_16:noUnderMulHurt


32_1:rebirth_enemy
32_2:recovery_enemy
32_3:recoveryHalo_enemy
32_4:reverseHurt_enemy
32_5:treater_FightWolf
32_6:sacrifice_equip
32_7:fleshFeast_pig
32_8:lockLife
32_9:liveReplace_enemy
32_10:upperLimitSecond

33_1:rifleSensitive
33_2:shotgunSensitive
33_3:sniperSensitive
33_4:pistolSensitive
33_5:rocketSensitive
33_6:flamerSensitive
33_7:crossbowSensitive
33_8:laserSensitive
33_9:otherSensitive
33_10:weaponSensitive
33_11:vehicleSensitive
33_12:petSensitive
33_13:redArmsSensitive
33_14:handSensitive

34_1:noBulletHurt
34_2:noUnderFlyHit
34_3:fightReduct
34_4:bladeShield
34_5:weaponDefence
34_6:fitVehicleDefence
34_7:ruleRange
34_8:likeMissle_Shapers
34_9:likeMissleNo
34_10:noBounce_enemy
34_11:rigidBody_enemy
34_12:thorns_pig
34_13:cmldef3_enemy
34_14:noUnderLaser
34_15:weaponNo
34_16:defenceBounce_enemy

35_1:State_SpellImmunity
35_2:immune
35_3:treater_knights
35_4:vertigoArmorIron
35_5:KingRabbitTreater
35_6:strongLing_10
35_7:spellImmunityMax





41_1:State_AddMove
41_2:State_AddMove50
41_3:groupSpeedUp_enemy
41_4:teleport_enemy
41_5:fastForward_enemy
41_6:noSpeedReduce2
41_7:strollCard
41_8:noMoveSpeed
41_9:jumpNumAdd1
41_10:jumpNumAddZero
41_11:zeroGravityCard
41_12:speedDrug
41_13:noSpeedReduce

42_1:hiding_enemy
42_2:hidingAll_enemy
42_3:invisibilityEver
42_4:invisibility_enemy
42_5:findHide
42_6:State_noAiFind

43_1:gmMask2
43_2:gmMask1
43_3:gmMask3
43_4:smallScale
43_5:bigScale
43_6:world180
43_7:worldBlack
43_8:rain1
43_9:rain2
43_10:rain3
43_11:heat1
43_12:heat2
43_13:heat3


44_1:State_noAllSkill
44_2:noAttackOrder
44_3:aiExcape
44_4:YouthWolfHeroAngerAdd
44_5:killMeTimeOver
44_6:firstLivePer10
44_7:noArmsShoot
44_8:sumBossAtten
44_9:mulHurtToValue
44_10:birthLock

		</body>
	</father>
		
</data>