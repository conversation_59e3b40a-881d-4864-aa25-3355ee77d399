<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="weaponSkill" cnName="副手技能">
		<skill><!-- 生存-群体-主动 -->
			<cnName>电离驱散</cnName><wantDescripB>1</wantDescripB>
			<name>weaponEmp</name>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>hurtIsAttack</otherConditionArr>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>weaponEmp</effectType>
			<valueString>feedback_</valueString>
			<!--图像------------------------------------------------------------ -->
			<pointEffectImg con="add">bulletHitEffect/fitHit</pointEffectImg>
			<description>清除目标电离折射效果。</description>
		</skill>
				<skill>
					<cnName>电离驱散-任何</cnName>
					<name>weaponEmpAll</name>
					<!--触发条件与目标------------------------------------------------------------ -->
					<conditionType>passive</conditionType>
					<condition>hit</condition>
					<target>target</target>
					<!--效果------------------------------------------------------------ -->
					<addType>instant</addType>
					<effectType>clearStateByNameContainPointEffect</effectType>
					<valueString>feedback_</valueString>
					<!--图像------------------------------------------------------------ -->
					<pointEffectImg con="add">bulletHitEffect/fitHit</pointEffectImg>
					<description>清除目标电离折射效果。</description>
				</skill>
		<![CDATA[
		<skill><!-- 生存-群体-主动 -->
			<cnName>加持</cnName><wantDescripB>1</wantDescripB>
			<name>goldSpadeSkill</name>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<applyArr>spadeAttack</applyArr>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>setEnemyStateTMul</effectType>
			<mul>2</mul>
			<!--图像------------------------------------------------------------ -->
			<pointEffectImg soundUrl="sound/hummer_hit">generalEffect/lightning</pointEffectImg>
			<description>击中怪物时，使它身上的负面效果时间延长1倍。</description>
		</skill>
		]]>
		<skill>
			<cnName>崩溃</cnName><wantDescripB>1</wantDescripB>
			<name>goldSpadeSkill</name><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>goldSpadeNumLess,noMainTask,targetUnitType</otherConditionArr>
			<conditionRange>2</conditionRange>
			<conditionString>boss</conditionString>
			<target unitType="boss">target</target>
			<applyArr>spadeAttack</applyArr>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>goldSpadeSkill</effectType>
			<mul>0.80</mul>
			<duration>999999</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg con="add" partType="body">generalEffect/nothing</stateEffectImg>
			<pointEffectImg soundUrl="sound/hummer_hit">generalEffect/lightning</pointEffectImg>
			<description>用铁锹敲打首领（主线任务、虚天塔除外），使其防御力每秒下降[1-mul]，无视技能免疫，每天只能释放2次该技能。</description>
		</skill>
		
		
		<skill index="0" name="凯撒特效附带"><!-- 生存-群体-主动 -->
			<name>sprintSwordHit</name>
			<cnName>凯撒特效附带</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<applyArr>swordAttack</applyArr>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>sprintSword</effectType>
			<duration>1</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
			<otherEffectImg con="add">Striker/swordAttackLeft</otherEffectImg>
			<pointEffectImg con="add">Striker/swordAttackRight</pointEffectImg>
		</skill>
		
		
		
		<skill index="0" name="凯撒特效附带"><!-- 生存-群体-主动 -->
		
		
		
			<name>sprintSwordHit_extra</name>
			<cnName>凯撒特效附带</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>no</condition>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>no</effectType>
			<!--图像------------------------------------------------------------ -->
			<otherEffectImg con="add">Striker/swordAfterAttackLeft</otherEffectImg>
			<pointEffectImg con="add">Striker/swordAfterAttackRight</pointEffectImg>
		</skill>
	</father>
</data>