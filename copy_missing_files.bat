@echo off
echo 正在复制缺失的文件从 binaryData 到 binaryData1...

copy "binaryData\334_XMLOut_vehicleSkillClass.bin" "binaryData1\"
copy "binaryData\335_XMLOut_armsNameClass.bin" "binaryData1\"
copy "binaryData\336_XMLOut_WandaClass.bin" "binaryData1\"
copy "binaryData\337_XMLOut_bwall_sayClass.bin" "binaryData1\"
copy "binaryData\338_XMLOut_BallLightningClass.bin" "binaryData1\"
copy "binaryData\339_XMLOut_cityUnitClass.bin" "binaryData1\"
copy "binaryData\340_XMLOut_unionBuildingClass.bin" "binaryData1\"
copy "binaryData\341_XMLOut__311_OreWormClass.bin" "binaryData1\"
copy "binaryData\342_XMLOut_activeTaskLevelClass.bin" "binaryData1\"
copy "binaryData\343_XMLOut_vehicleClass.bin" "binaryData1\"
copy "binaryData\344_XMLOut_crossbowClass.bin" "binaryData1\"
copy "binaryData\345_XMLOut_giftClass.bin" "binaryData1\"
copy "binaryData\346_XMLOut_level_XiShanSayClass.bin" "binaryData1\"
copy "binaryData\347_XMLOut_loveSkillXinLingClass.bin" "binaryData1\"
copy "binaryData\348_XMLOut_wilderEnemyBulletClass.bin" "binaryData1\"
copy "binaryData\349_XMLOut_exploitCardsClass.bin" "binaryData1\"
copy "binaryData\350_XMLOut_OfficeZombieClass.bin" "binaryData1\"
copy "binaryData\351_XMLOut_penGunClass.bin" "binaryData1\"
copy "binaryData\352_XMLOut_flamerClass.bin" "binaryData1\"
copy "binaryData\353_XMLOut_PetLaerSClass.bin" "binaryData1\"
copy "binaryData\354_XMLOut_otherLevelClass.bin" "binaryData1\"
copy "binaryData\355_XMLOut_petStrengthenClass.bin" "binaryData1\"
copy "binaryData\356_XMLOut_bwall_levelClass.bin" "binaryData1\"
copy "binaryData\357_XMLOut_loveTalkClass.bin" "binaryData1\"
copy "binaryData\358_XMLOut__201_meteoroliteClass.bin" "binaryData1\"
copy "binaryData\359_XMLOut_wilderEnemyClass.bin" "binaryData1\"
copy "binaryData\361_XMLOut_cityBodyClass.bin" "binaryData1\"
copy "binaryData\362_XMLOut_giftHomeClass.bin" "binaryData1\"
copy "binaryData\363_XMLOut_level_HanGuang5_1Class.bin" "binaryData1\"
copy "binaryData\364_XMLOut_itemsStrengthenClass.bin" "binaryData1\"
copy "binaryData\365_XMLOut_level_Hospital4_1Class.bin" "binaryData1\"
copy "binaryData\366_XMLOut_MadbossClass.bin" "binaryData1\"
copy "binaryData\367_XMLOut_aiTorProClass.bin" "binaryData1\"

echo 复制完成！
echo 已复制 33 个文件到 binaryData1 文件夹
pause
