<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="we" cnName="我方">
		<body name="扶光" shell="compound">
			
			<name><PERSON><PERSON><PERSON></name>
			<cnName>扶光</cnName>
			<raceType>human</raceType>
			<swfUrl>swf/hero/FuGuang.swf</swfUrl>
			<!-- 基本系数 -->
			<showLevel>9999</showLevel>
			<headIconUrl>fuGuang/head_icon</headIconUrl>
			<imgType>normal</imgType>
			<imgArr>
				stand,move
				,normalAttack,die1
				,comboAttack,mopAttack,daggerAttack,shakeAttack
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-12, -90, 24, 90</hitRect><!-- 站立碰撞体积-->
			<maxVx>12</maxVx>
			<avtiveSkillCdOverT>0.1</avtiveSkillCdOverT>
			<!-- 技能 -->
			<skillArr>FuGuangBuff,noDegradation,vertigoArmorIron,meltFlamerPurgold</skillArr>
			<bossSkillArr>addWangShu,FuGuangCombo,FuGuangShake,FuGuangMop</bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<wilderSkillArr></wilderSkillArr>
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel><cn>挥砍</cn>
					<hurtRatio>1</hurtRatio>
					<attackType>holy</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl name="redSteel"/>
				</hurt>
				<hurt cd="3">
					<imgLabel>daggerAttack</imgLabel><cn>金炎</cn>
					<hurtRatio>3</hurtRatio>
					<attackType>holy</attackType>
					<bulletLabel>FuGuang_1</bulletLabel>
					<skillArr>invincibleEmp,silence_SnowSoldiers</skillArr>
					<grapRect>-700,-150,400,230</grapRect>
					<hitImgUrl name="redSteel"/>
				</hurt>
				<![CDATA[以下不加入ai选择]]>
				<hurt>
					<imgLabel>comboAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.00000000000001</hurtRatio>
					<hurtMul>0.1</hurtMul>
					<attackType>holy</attackType>
					<skillArr>invincibleEmp</skillArr>
					<hitImgUrl name="redSteel"/>
				</hurt>
				<hurt>
					<imgLabel>shakeAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.00000000000001</hurtRatio>
					<hurtMul>0.3</hurtMul>
					<attackType>holy</attackType>
					<skillArr>invincibleEmp</skillArr>
					<hitImgUrl name="redSteel"/>
				</hurt>
				<hurt>
					<imgLabel>mopAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.00000000000001</hurtRatio>
					<hurtMul>0.05</hurtMul>
					<attackType>holy</attackType>
					<skillArr>invincibleEmp</skillArr>
					<hitImgUrl name="redSteel"/>
				</hurt>
				
			</hurtArr>
		</body>
	</father>
	<father name="enemy" cnName="技能">
		<bullet>
			<name>FuGuang_1</name>
			<cnName>扶光剑气</cnName><noMagneticB>1</noMagneticB>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>179.9</bulletAngle>
			<bulletLife>3</bulletLife>
			<bulletWidth>50</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.3</attackGap>
			<attackDelay>0.6</attackDelay>
			<bulletNum>1</bulletNum>				
			<shootNum>6</shootNum>					
			<shootGap>0.13333</shootGap>									
			<positionD>
				<point shootPoint="-150,-4" bulletAngle="161" />
				<point shootPoint="-153,-48" bulletAngle="179.9" />
				<point shootPoint="-150,-82" bulletAngle="-161" />
				<point shootPoint="-150,-4" bulletAngle="161" />
				<point shootPoint="-153,-48" bulletAngle="179.9" />
				<point shootPoint="-150,-82" bulletAngle="-161" />
			</positionD>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,0</shootPoint>
			<bulletSpeed>50</bulletSpeed>
			<speedD random="0.3"/>
			<gravity>0</gravity>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl name="FuGuangBullet"/>
			<hitImgUrl name="redSteel"/>
		</bullet>
		
		
	</father>
	
	
	<father name="enemy" cnName="技能">
		<skill>
			<name>addWangShu</name>
			<cnName>祈月</cnName><noCopyB>1</noCopyB><everNoClearB>1</everNoClearB><noBeClearB>1</noBeClearB><ignoreNoSkillB>1</ignoreNoSkillB><ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>passive</conditionType><condition>addAndCon</condition><wantDescripB>1</wantDescripB>
			<target>me</target>
			<addType>instant</addType>
			<summonedUnitsB>1</summonedUnitsB>
			<effectType>summonedUnits</effectType>
			<duration>99999999</duration>
			<!-- 子弹所需 -->
			<obj>"cnName":"望舒","num":1,"lifeMul":1,"dpsMul":1,"mulByFatherB":1,"maxNum":1,"lifeBarB":1,"unitType":"super"</obj>
			<description>召唤望舒和自己并肩作战；望舒带有无敌光环、技能免疫、化锁、眩晕护甲等技能。</description>
		</skill>
		
		<skill>
			<name>FuGuangBuff</name>
			<cnName>扶光buff</cnName><noCopyB>1</noCopyB><everNoClearB>1</everNoClearB><noBeClearB>1</noBeClearB><ignoreNoSkillB>1</ignoreNoSkillB><ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>passive</conditionType><condition>add</condition><target>me</target>
			<addType>state</addType>
			<effectType>FuGuangBuff</effectType><effectFather>extraBody</effectFather>
			<duration>99999999</duration>
		</skill>
		
		<skill>
			<name>FuGuangCombo</name>
			<cnName>斗转</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>6</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition><target>me</target>
			<addType>instant</addType><effectType>no</effectType>
			<meActionLabel>comboAttack</meActionLabel>
			<description>瞬移到敌人面前，发动2连击。</description>
		</skill>
		<skill>
			<name>FuGuangShake</name>
			<cnName>羲落</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>8</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition><target>me</target>
			<addType>instant</addType><effectType>no</effectType>
			<meActionLabel>shakeAttack</meActionLabel>
			<description>跳跃至敌人上方，发动跳斩。</description>
		</skill>
		<skill>
			<name>FuGuangMop</name>
			<cnName>星移</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>12</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition><target>me</target>
			<addType>instant</addType><effectType>no</effectType>
			<meActionLabel>mopAttack</meActionLabel>
			<description>以极快的速度左右横扫敌人。</description>
		</skill>
		
	</father>	
</data>