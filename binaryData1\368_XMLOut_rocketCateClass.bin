<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father  type="rocket" cnName="火炮-特殊">
		<bullet index="40" cnName="卡特巨炮" name="rocketCate" color="black" dropLevelArr="94" dropBodyArr="FireDragon" evoMaxLv="10" evoMustFirstLv="4" composeLv="94" chipNum="150">
			<n>rocketCate</n>
			<cnName>卡特巨炮</cnName>
			<!--随机属性------------------------------------------------------------ -->
			<!--基本-->
			<capacity>7</capacity>
			<attackGap>1.4</attackGap>
			<reloadGap>3</reloadGap>
			<shakeAngle>0</shakeAngle>
			<bulletWidth>30</bulletWidth>
			<bulletShakeWidth>0,0</bulletShakeWidth>
			<bulletNum>10</bulletNum>
			<shootAngle>60</shootAngle>
			<gunNum>1</gunNum>
			
			<!--武器属性------------------------------------------------------------ -->
			<armsArmMul>0.65</armsArmMul>
			<upValue>5</upValue>
			<shootShakeAngle>40</shootShakeAngle>
			<shootRecoil>12</shootRecoil>
			<screenShakeValue>16</screenShakeValue>
			<!--基本属性------------------------------------------------------------ -->
			<noShakeTime>0.5</noShakeTime>
			<bulletLife>0.9</bulletLife>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<dpsMul>1.8</dpsMul>
			<uiDpsMul>2</uiDpsMul>
			
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>35</bulletSpeed>
			<!--特殊------------------------------------------------------------ -->
			<boomD  bodyB="1" floorB="1" selfB="1" radius="130"/>
			<twoShootPro>0.3</twoShootPro>
			<penetrationNum>2</penetrationNum>
			<penetrationGap>0</penetrationGap>
			<bounceD floor="0" body="3"/>	<!-- 反弹 -->
			<followD value="0.5"/>
			<critD mul="2" pro="0.2"/>
			
			
			<skillArr>Hit_blindness_ArmsSkill,Hit_disabled_ArmsSkill</skillArr>
			<godSkillArr>Hit_imploding_godArmsSkill,cate_ArmsSkill</godSkillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<iconUrl></iconUrl>
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">bullet/gaiaBullet</bulletImgUrl>
			<bulletSoundUrl>sound/rocket</bulletSoundUrl>
			<bulletHitSoundUrl>sound/rocket_hit</bulletHitSoundUrl>
			<bulletHitImgUrl>bulletHitEffect/rocketHit</bulletHitImgUrl>
			<bulletShakeValue>10</bulletShakeValue>
			<bulletShakeTime>0.2</bulletShakeTime>
			<bulletShakeNum>5</bulletShakeNum>
			<bulletShakeGap>0.1</bulletShakeGap>
			<bulletShakeAngle>0</bulletShakeAngle>
			<bulletShakeAngleRandom>0</bulletShakeAngleRandom>
			<bulletShakeDistance>0</bulletShakeDistance>
			<bulletShakeDistanceRandom>0</bulletShakeDistanceRandom>
			<bulletShakeAlpha>0</bulletShakeAlpha>
			<bulletShakeAlphaRandom>0</bulletShakeAlphaRandom>
			<bulletShakeScale>0</bulletShakeScale>
			<bulletShakeScaleRandom>0</bulletShakeScaleRandom>
			<bulletShakeColor>0</bulletShakeColor>
			<bulletShakeColorRandom>0</bulletShakeColorRandom>
			<bulletShakeRotate>0</bulletShakeRotate>
			<bulletShakeRotateRandom>0</bulletShakeRotateRandom>
			<bulletShakeX>0</bulletShakeX>
			<bulletShakeXRandom>0</bulletShakeXRandom>
			<bulletShakeY>0</bulletShakeY>
			<bulletShakeYRandom>0</bulletShakeYRandom>
			<bulletShakeZ>0</bulletShakeZ>
			<bulletShakeZRandom>0</bulletShakeZRandom>
		</bullet>
	</father>
</data>
