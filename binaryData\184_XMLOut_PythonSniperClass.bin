<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="snake" cnName="血蟒">
		<body index="0" name="血蟒狙击兵" shell="normal">
			
			<name>PythonSniper</name><movieLink>XiaoHu</movieLink>
			<cnName>血蟒狙击兵</cnName>
			<raceType>human</raceType>
			<swfUrl>swf/hero/PythonSniper.swf</swfUrl>
			<!-- 基本系数 -->
			<showLevel>9999</showLevel>
			<headIconUrl>IconGather/PythonSniper</headIconUrl>
			<lifeBarExtraHeight>-20</lifeBarExtraHeight>
			<!-- 碰撞体积 -->
			<hitRect>-12, -90, 24, 90</hitRect><!-- 站立碰撞体积-->
			<squatHitRect>-12,-50,24,50</squatHitRect><!-- 下蹲碰撞体积-->
			<hurtRectArr>-15,-38,30,38</hurtRectArr> <!-- 要害受伤体积-->
			<hurtRectArr>-12, -100, 24, 95</hurtRectArr> <!-- 站立受伤体积-->
			<hurtRectArr>-12, -50, 24, 50</hurtRectArr> <!-- 下蹲受伤体积-->
			<!-- 运动 -->
			<maxVx>11</maxVx>
			<squatMaxVx>5</squatMaxVx>
			<maxJumpNum>2</maxJumpNum>
			<flyType>space</flyType><motionD F_AIR="6" />
			<!-- AI属性 -->
			<armsNumber>1</armsNumber><!-- 武器个数 -->
			<randomArmsRange>sniperRifle</randomArmsRange>
			<!-- 技能 -->
			<skillArr>hyperopia_incapable</skillArr>
			<bossSkillArr>sumLandDog,slowMove_enemy</bossSkillArr>
		</body>
	</father>
	<father name="snake" cnName="血蟒">
		<skill>
			<name>sumAirDog</name><iconUrl36>SkillIcon/summonedSpider_spiderKing_36</iconUrl36>
			<cnName>召唤飞天狗</cnName>
			<showInLifeBarB>1</showInLifeBarB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>4</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>1500</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<!-- 子弹所需 -->
			<obj>"cnName":"飞天狗","num":1,"lifeMul":0.015,"maxNum":20,"mulByFatherB":1,"pointEf":1,"cx":0,"cy":-104,"skillArr":["State_AddMove50"]</obj>
			<!--图像------------------------------------------------------------ -->
			<pointEffectImg name="oreBombShowFlower"/>
			<description>召唤[obj.num]只[obj.cnName]参加战斗，最多同时存在[obj.maxNum]只[obj.cnName]。</description>
		</skill>
		<skill>
			<name>sumLandDog</name><iconUrl36>SkillIcon/summonedSpider_spiderKing_36</iconUrl36>
			<cnName>召唤巡逻狗</cnName>
			<showInLifeBarB>1</showInLifeBarB>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>5</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>1500</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<!-- 子弹所需 -->
			<obj>"cnName":"巡逻狗","num":1,"lifeMul":0.015,"maxNum":20,"mulByFatherB":1,"pointEf":1,"cx":0,"cy":-50,"skillArr":["State_AddMove50"]</obj>
			<!--图像------------------------------------------------------------ -->
			<pointEffectImg name="oreBombShowFlower"/>
			<description>召唤[obj.num]只[obj.cnName]参加战斗，最多同时存在[obj.maxNum]只[obj.cnName]。</description>
		</skill>
	</father>
</data>