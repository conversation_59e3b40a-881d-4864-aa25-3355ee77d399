<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="device" cnName="装置">
		<![CDATA[次数装置，用完后自动删除。任务专属]]>
		<equip rareB="1" canNum="1" name="hammerMine" cnName="眩晕地雷T" skill="hammerMine" iconLabel="dropEffect/hammerMine" />
	</father>
	<father name="deviceSkill" cnName="装置技能">
		<skill cnName="眩晕地雷">
			<name>hammerMine</name>
			<cnName>眩晕地雷</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>20</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<doCondition>nearNoMine</doCondition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<extraValueType>nowArmsDpsOrNormal</extraValueType>
			<obj>"name":"hammerMineBullet","site":"meMid","minYB":true</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg name="putMineEffect"/>
			<description>埋下可眩晕敌人5秒的眩晕地雷。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><cd>20</cd></skill>
				<skill><cd>15</cd></skill>
				<skill><cd>10</cd></skill>
				<skill><cd>8</cd></skill>
				<skill><cd>6</cd></skill>
				<skill><cd>1</cd></skill>
			</growth>
		</skill>
				<skill><!-- 限制 -->
					<name>hammerMineHit</name><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB><noBeClearB>1</noBeClearB>
					<cnName>击中眩晕</cnName>
					<!--触发条件与目标------------------------------------------------------------ -->
					<conditionType>passive</conditionType>
					<condition>hit</condition>
					<target>target</target>
					<!--效果------------------------------------------------------------ -->
					<addType>instantAndState</addType>
					<effectType>dizziness</effectType>
					<duration>5</duration>
					<!--图像------------------------------------------------------------ -->
					<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
				</skill>
		
		<bullet>
			<name>hammerMineBullet</name>
			<cnName>眩晕地雷-子弹</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>90</bulletAngle>
			<bulletLife>9999999</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--特殊属性------------------------------------------------------------ -->	
			<noHitTime>1</noHitTime>
			<hideTime>5</hideTime>
			<boomD  selfB="1" radius="270" bodyB="1"  maxHurtNum="999"/>
			<bounceD glueFloorB="1" />
			<skillArr>hammerMineHit</skillArr>
			<bulletSkillArr></bulletSkillArr>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>0</bulletSpeed> 
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl name="hammerMineBulletEnemy"/>
			<bulletLeftImgUrl name="hammerMineBullet"/>
			<selfBoomImgUrl name="bigSoilShort"/><!-- 子弹图像【必备】 --><!-- 子弹图像【必备】 -->
		</bullet>
	</father>
	<father name="dropEffect" cnName="掉落的效果物品">
		<skill>
			<name>hammerMineDrop</name>
			<cnName>获得眩晕地雷</cnName>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>addTempDevice</effectType>
			<value>2</value>
			<valueString>hammerMine_6</valueString>
		</skill>
	</father>
</data>