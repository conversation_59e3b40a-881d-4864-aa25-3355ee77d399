<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="normal">
		<gather name="千叶谷">
			<level name="<PERSON>an<PERSON>e_sin">
				<!-- 发兵集************************************************ -->
				<info enemyLv="82"/>
				<fixed target="QianYe_1" info="no" drop="all" unitG="all" rectG="all" eventG="no"/>
				<!-- 基本属性 -->
				<sceneLabel>QianYe</sceneLabel>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e1_1"><condition delay="2"></condition><order>say; startList:s1</order></event>
						<event id="e2_0"><condition>say:listOver; s1</condition></event><event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11"><condition delay="1">enemyNumber:less_1</condition><order>level; rebirthAllMore</order></event>	
						<event id="e2_11"><condition delay="1"></condition><order>say; startList:s2</order></event>	
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s2</condition>
							<order>task:now; complete</order>
							<order>worldMap:levelName; QianYe:QianYe_1</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>
			<level name="QianYe_1">
				<!-- 发兵集************************************************ -->
				<info enemyLv="82"/><sceneLabel>QianYe</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="独眼僵尸" num="6"/>
						<unit cnName="银锤" num="5"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="独眼僵尸" num="6"/>
						<unit cnName="银锤" num="6"/>
						<unit cnName="哨兵"  num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="独眼僵尸" num="4"/>
						<unit cnName="银锤" num="6"/>
						<unit cnName="哨兵" num="4.5"  />
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="窃听者" unitType="boss" lifeMul="1.8" dpsMul="1.8"/>
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">36,738,190,70</rect> 				<rect id="r_over">2093,632,38,96</rect>
					<rect id="r1">42,112,344,106</rect> 					<rect id="r2">943,132,356,104</rect> 						<rect id="r3">1725,146,356,108</rect>
					<rect label="addCharger">178,326,45,45</rect> 	<rect label="addCharger">1613,640,45,45</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		<gather name="腐木岗">
			<level name="FuMu_creation">
				<!-- 发兵集************************************************ -->
				<info enemyLv="83"/>
				<fixed target="FuMu_1" info="no" drop="all" unitG="all" rectG="all" eventG="no"/>
				<!-- 基本属性 -->
				<sceneLabel>FuMu</sceneLabel>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e1_1"><condition delay="2"></condition><order>say; startList:s1</order></event>
						<event id="e2_0"><condition>say:listOver; s1</condition></event><event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11"><condition delay="1">enemyNumber:less_1</condition><order>level; rebirthAllMore</order></event>	
						<event id="e2_11"><condition delay="1"></condition><order>say; startList:s2</order></event>	
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s2</condition>
							<order>task:now; complete</order>
							<order>worldMap:levelName; FuMu:FuMu_1</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>
			<level name="FuMu_1">
				<!-- 发兵集************************************************ -->
				<info enemyLv="83"/><sceneLabel>FuMu</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="监狱僵尸" num="6"/>
						<unit cnName="毒蛛" num="5"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="监狱僵尸" num="6"/>
						<unit cnName="毒蛛" num="6"/>
						<unit cnName="窃听者"  num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="监狱僵尸" num="4"/>
						<unit cnName="毒蛛" num="6"/>
						<unit cnName="窃听者" num="4.5"  />
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="伏地尸" unitType="boss" lifeMul="2.6" dpsMul="1.5"/>
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">28,768,268,136</rect> 			<rect id="r_over">3768,828,60,132</rect>
					<rect id="r1">500,288,688,196</rect> 					<rect id="r2">1748,236,684,196</rect> 						<rect id="r3">2928,256,524,148</rect>
					<rect label="addCharger">1360,588,60,60</rect> <rect label="addCharger">3200,532,60,60</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		<gather name="公墓入口">
			<level name="GongMu_guardian">
				<!-- 发兵集************************************************ -->
				<info enemyLv="84"/>
				<fixed target="GongMu_1" info="no" drop="all" unitG="all" rectG="all" eventG="no"/>
				<!-- 基本属性 -->
				<sceneLabel>GongMu</sceneLabel>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11"><condition delay="1">enemyNumber:less_1</condition><order>level; rebirthAllMore</order></event>	
						<event id="e2_11"><condition delay="1"></condition><order>say; startList:s2</order></event>	
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s2</condition>
							<order>task:now; complete</order>
							<order>worldMap:levelName; GongMu:GongMu_1</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>
			<level name="GongMu_1">
				<!-- 发兵集************************************************ -->
				<info enemyLv="84"/><sceneLabel>GongMu</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="哨兵" num="6"/>
						<unit cnName="伏地尸" num="5"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="哨兵" num="6"/>
						<unit cnName="伏地尸" num="6"/>
						<unit cnName="窃听者"  num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="哨兵" num="4"/>
						<unit cnName="伏地尸" num="6"/>
						<unit cnName="窃听者" num="4.5"  />
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="掘金尸" unitType="boss" lifeMul="2.6" dpsMul="2.5"/>
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">58,768,150,96</rect> 				<rect id="r_over">2535,1410,58,103</rect>
					<rect id="r1">50,1060,431,134</rect> 					<rect id="r2">1396,700,373,112</rect> 						<rect id="r3">2629,566,352,126</rect>
					<rect label="addCharger">1047,850,60,60</rect> <rect label="addCharger">2853,1432,60,60</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		<gather name="鬼王墓">
			<level name="GuiWang_breath">
				<!-- 发兵集************************************************ -->
				<info enemyLv="85"/>
				<fixed target="GuiWang_1" info="no" drop="all" unitG="all" rectG="all" eventG="no"/>
				<!-- 基本属性 -->
				<sceneLabel>GuiWang</sceneLabel>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e1_1"><condition delay="2"></condition><order>say; startList:s1</order></event>
						<event id="e2_0"><condition>say:listOver; s1</condition></event><event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11"><condition delay="1">enemyNumber:less_1</condition><order>level; rebirthAllMore</order></event>	
						<event id="e2_11"><condition delay="1"></condition><order>say; startList:s2</order></event>	
						<event id="e2_11">
							<condition delay="0.5">say:listOver; s2</condition>
							<order>task:now; complete</order>
							<order>worldMap:levelName; GuiWang:GuiWang_1</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>
			<level name="GuiWang_1">
				<!-- 发兵集************************************************ -->
				<info enemyLv="85"/><sceneLabel>GuiWang</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="伏地尸" num="6"/>
						<unit cnName="窃听者" num="5"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="伏地尸" num="6"/>
						<unit cnName="窃听者" num="6"/>
						<unit cnName="掘金尸"  num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="伏地尸" num="4"/>
						<unit cnName="窃听者" num="6"/>
						<unit cnName="掘金尸" num="4.5" />
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="狂野收割者" unitType="boss" lifeMul="3" dpsMul="2"/>
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">200,1432,86,106</rect> 			<rect id="r_over">538,322,44,96</rect>
					<rect id="r1">90,730,346,88</rect> 						<rect id="r2">1006,300,346,91</rect> 						<rect id="r3">1582,812,340,120</rect>
					<rect label="addCharger">158,400,42,42</rect> <rect label="addCharger">1298,1058,48,48</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
	</father>
</data>