#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量导入binaryData1中的所有配置文件到SWF
"""

import os
import glob

def generate_import_commands():
    """生成批量导入命令"""
    
    # 获取binaryData1文件夹中的所有.bin文件
    bin_files = glob.glob("binaryData1/*.bin")
    bin_files.sort()  # 按文件名排序
    
    print(f"找到 {len(bin_files)} 个配置文件")
    print("\n=== 批量导入命令 ===")
    print("请在SWF编辑器中依次执行以下操作：\n")
    
    for i, file_path in enumerate(bin_files, 1):
        filename = os.path.basename(file_path)
        # 提取文件编号和类名
        parts = filename.replace('.bin', '').split('_XMLOut_')
        if len(parts) == 2:
            file_id = parts[0]
            class_name = parts[1].replace('Class', '')
            
            print(f"{i:3d}. 导入 {filename}")
            print(f"     - 右键 DefineBinaryData (chid: {file_id}, cls: XMLOut_{class_name}Class)")
            print(f"     - 选择 '替换...' 或 'Replace...'")
            print(f"     - 选择文件: {file_path}")
            print(f"     - 确认导入")
            print()
    
    print("=== 快速操作提示 ===")
    print("1. 可以使用Ctrl+F在SWF编辑器中快速搜索对应的DefineBinaryData")
    print("2. 建议先备份原始SWF文件")
    print("3. 导入完成后记得保存SWF文件")
    print("4. 如果某些文件在SWF中不存在，可以跳过")

def generate_batch_file():
    """生成Windows批处理文件用于快速复制文件路径"""
    
    bin_files = glob.glob("binaryData1/*.bin")
    bin_files.sort()
    
    with open("file_paths.txt", "w", encoding="utf-8") as f:
        f.write("=== binaryData1 文件路径列表 ===\n\n")
        for file_path in bin_files:
            abs_path = os.path.abspath(file_path)
            f.write(f"{abs_path}\n")
    
    print(f"\n已生成 file_paths.txt 文件，包含所有文件的完整路径")
    print("你可以从该文件中复制路径，方便在SWF编辑器中选择文件")

def main():
    """主函数"""
    print("=== binaryData1 批量导入工具 ===\n")
    
    if not os.path.exists("binaryData1"):
        print("错误：找不到 binaryData1 文件夹")
        return
    
    generate_import_commands()
    generate_batch_file()
    
    print("\n=== 重要提醒 ===")
    print("1. 这是作弊版本配置，所有物品价格为0，购买限制为9999")
    print("2. 导入前请备份原始SWF文件")
    print("3. 导入顺序不重要，但建议按编号顺序进行")
    print("4. 如果遇到错误，可以单独导入出问题的文件")

if __name__ == "__main__":
    main()
