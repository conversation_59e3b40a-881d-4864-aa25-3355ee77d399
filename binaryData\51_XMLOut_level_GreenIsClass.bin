<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="normal">
		<gather cnName="绿岛外">
			<level name="YaShipsComing">
				<!-- 发兵集************************************************ -->
				<info enemyLv="99" diff="2" allMoreB="1" noPetB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" />
				<sceneLabel>MainUpland</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="we1" camp="we"><unit id="u1" cnName="氩星舰" unitType="boss" /></unitOrder>
					<unitOrder id="we2" camp="we"><unit id="u2" cnName="氩星舰" unitType="boss"/></unitOrder>
					<unitOrder id="we3" camp="we"><unit id="u3" cnName="氩星舰" unitType="boss"/></unitOrder>
					<unitOrder id="we4" camp="we"><unit id="u4" cnName="氩星舰" unitType="boss"/></unitOrder>
					<unitOrder id="we5" camp="we"><unit id="u5" cnName="氩星舰" unitType="boss"/></unitOrder>
					<unitOrder id="we6" camp="we"><unit id="u6" cnName="氩星舰" unitType="boss"/></unitOrder>
					
					<unitOrder id="we11" camp="we"><unit id="u11" cnName="氩星舰" skillArr="smallScale50" /></unitOrder>
					<unitOrder id="we12" camp="we"><unit id="u12" cnName="氩星舰" skillArr="smallScale50" /></unitOrder>
					<unitOrder id="we13" camp="we"><unit id="u13" cnName="氩星舰" skillArr="smallScale50" /></unitOrder>
					<unitOrder id="we14" camp="we"><unit id="u14" cnName="氩星舰" skillArr="smallScale50" /></unitOrder>
					<unitOrder id="we15" camp="we"><unit id="u15" cnName="氩星舰" skillArr="smallScale50" /></unitOrder>
					<unitOrder id="we16" camp="we"><unit id="u16" cnName="氩星舰" skillArr="smallScale50" /></unitOrder>
					<unitOrder id="we17" camp="we"><unit id="u17" cnName="氩星舰" skillArr="smallScale50" /></unitOrder>
					
					<unitOrder id="we18" camp="we"><unit id="u18" cnName="氩星舰" skillArr="smallScale50" /></unitOrder>
					<unitOrder id="we19" camp="we"><unit id="u19" cnName="氩星舰" skillArr="smallScale50" /></unitOrder>
					<unitOrder id="we20" camp="we"><unit id="u20" cnName="氩星舰" skillArr="smallScale50" /></unitOrder>
					<unitOrder id="we21" camp="we"><unit id="u21" cnName="氩星舰" skillArr="smallScale50" /></unitOrder>
				</unitG>
				<rectG>
					<rect id="y1">80,145,1,1</rect>
					<rect id="y2">697 ,145,1,1</rect>
					<rect id="y3">1424 ,145,1,1</rect>
					<rect id="y4">309 ,344,1,1</rect>
					<rect id="y5">1068 ,344,1,1</rect>
					<rect id="y6">1818 ,344,1,1</rect>
					
					<rect id="y11">216 ,250,1,1</rect>
					<rect id="y12">837,250,1,1</rect>
					 <rect id="y13">1231,250,1,1</rect>
					<rect id="y14">1668,250,1,1</rect>
					
					<rect id="y15">523,50,1,1</rect>
					<rect id="y16">999,50,1,1</rect>
					<rect id="y17">1443,50,1,1</rect>
					
					<rect id="y18">216 ,-50,1,1</rect>
					<rect id="y19">837,-50,1,1</rect>
					 <rect id="y20">1231,-50,1,1</rect>
					<rect id="y21">1668,-50,1,1</rect>
				</rectG> 
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event>
							<condition delay="0.01"></condition>
							<order>recoverAllHeroHead</order>
							<order>noWearShop</order>
						</event>
						<event>
							<condition delay="0.1"></condition>
							<order>body:小樱; followBody:文杰表哥</order>
							<order>body:藏师将军; followBody:小樱</order>
							<order>body:心零; followBody:藏师将军</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>level; shake:beatMadboss</order>
						</event>
						<event>
							<condition delay="1.5"></condition>
							<order>say; startList:s11</order>
						</event>
						<event><condition>say:listOver</condition></event>
						<![CDATA[氩星舰出现]]>
						<event><condition delay="0.1"></condition><order>createUnit:we18; y18:L_mid</order></event>
						<event><condition delay="0.1"></condition><order>createUnit:we19; y19:L_mid</order></event>
						<event><condition delay="0.1"></condition><order>createUnit:we20; y20:L_mid</order></event>
						<event><condition delay="0.1"></condition><order>createUnit:we21; y21:L_mid</order></event>
						
						<event><condition delay="0.1"></condition><order>createUnit:we15; y15:L_mid</order></event>
						<event><condition delay="0.1"></condition><order>createUnit:we16; y16:L_mid</order></event>
						<event><condition delay="0.1"></condition><order>createUnit:we17; y17:L_mid</order></event>
						
						<event><condition delay="0.1"></condition><order>createUnit:we11; y11:L_mid</order></event>
						<event><condition delay="0.1"></condition><order>createUnit:we12; y12:L_mid</order></event>
						<event><condition delay="0.1"></condition><order>createUnit:we13; y13:L_mid</order></event>
						<event><condition delay="0.1"></condition><order>createUnit:we14; y14:L_mid</order></event>
						
						<event><condition delay="0.2"></condition><order>createUnit:we1; y3</order><order>level; shake:ArgonShip</order></event>
						<event><condition delay="0.2"></condition><order>createUnit:we2; y2</order><order>level; shake:ArgonShip</order></event>
						<event><condition delay="0.2"></condition><order>createUnit:we3; y1</order><order>level; shake:ArgonShip</order></event>
						<event><condition delay="0.2"></condition><order>createUnit:we4; y4</order><order>level; shake:ArgonShip</order></event>
						<event><condition delay="0.2"></condition><order>createUnit:we6; y6</order><order>level; shake:ArgonShip</order></event>
						<event><condition delay="0.2"></condition><order>createUnit:we5; y5</order><order>level; shake:ArgonShip</order><order>playMusic; music_face:Madboss</order></event>
						<event>
							<condition delay="0.1"></condition>
							<order>say; startList:s2</order>
						</event>
						<![CDATA[缩放场景]]>
						<event><condition delay="2"></condition><order>sceneScale:0.75</order><order>playSound:uiSound/giveupTask</order></event>
						<event><condition delay="1"></condition><order>sceneScale:0.50</order><order>playSound:uiSound/giveupTask</order></event>
						<event><condition delay="3"></condition><order>sceneScale:1</order><order>playSound:uiSound/getTask</order></event>
						
						<![CDATA[氩星舰消失]]>
						<event>
							<condition>say:listOver</condition>
						</event>
						
						<event><condition delay="0.2"></condition><order>body:u5;playBreak:disappearAttack</order></event>
						<event><condition delay="0.2"></condition><order>body:u1;playBreak:disappearAttack</order></event>
						<event><condition delay="0.2"></condition><order>body:u2;playBreak:disappearAttack</order></event>
						<event><condition delay="0.2"></condition><order>body:u3;playBreak:disappearAttack</order></event>
						<event><condition delay="0.2"></condition><order>body:u4;playBreak:disappearAttack</order></event>
						<event><condition delay="0.2"></condition><order>body:u6;playBreak:disappearAttack</order></event>
						<event><condition></condition><order>sceneScale:0.7</order></event>
						<event><condition delay="0.1"></condition><order>body:u11;playBreak:disappearAttack</order></event>
						<event><condition delay="0.1"></condition><order>body:u12;playBreak:disappearAttack</order></event>
						<event><condition delay="0.1"></condition><order>body:u13;playBreak:disappearAttack</order></event>
						<event><condition delay="0.1"></condition><order>body:u14;playBreak:disappearAttack</order></event>
						<event><condition delay="0.1"></condition><order>body:u15;playBreak:disappearAttack</order></event>
						<event><condition delay="0.1"></condition><order>body:u16;playBreak:disappearAttack</order></event>
						<event><condition delay="0.1"></condition><order>body:u17;playBreak:disappearAttack</order></event>
						<event><condition delay="0.1"></condition><order>body:u18;playBreak:disappearAttack</order></event>
						<event><condition delay="0.1"></condition><order>body:u19;playBreak:disappearAttack</order></event>
						<event><condition delay="0.1"></condition><order>body:u20;playBreak:disappearAttack</order></event>
						<event><condition delay="0.1"></condition><order>body:u21;playBreak:disappearAttack</order></event>
						<event><condition delay="1"></condition><order>sceneScale:1</order></event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s3</order>
							<order>playMusic; music_face:funny</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>say; startList:s4</order>
							<order>playMusic; music_face:suspense</order>
						</event>
						<event>
							<condition>say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="RaysSeaBreak">
				<!-- 关卡数据 -->
				<info enemyLv="99" preBulletArr=""  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" firstLostB="1" />
				<drop noB="1"/>
				<!-- 基本属性 -->
				<sceneLabel>RaysSea</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<unitOrder id="we1" camp="we">
						<unit id="weBoss" cnName="处决者"  lifeMul="0.18" dpsMul="500" skillArr="crazy_vehicle_8,groupLightMadFly" />
					</unitOrder>
					<unitOrder id="we2" camp="we"><![CDATA[用于初始对话]]>
						<numberType>num</numberType>
						<unit cnName="大鹏" num="1" lifeMul="2" />
					</unitOrder>
					
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="大鹏" num="1" lifeMul="2"/>
						<unit cnName="潜伏者" num="1" lifeMul="2"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="大鹏" num="2" lifeMul="2"/>
						<unit cnName="潜伏者" num="2" lifeMul="2"/>
					</unitOrder>
					<unitOrder id="super1">
						<numberType>num</numberType>
						<unit cnName="潜影者" lifeMul="2" unitType="super" skillArr="crazy_vehicle_5,groupLight_enemy" />
					</unitOrder>
					<unitOrder id="boss1">
						<numberType>num</numberType>
						<unit cnName="鲲" lifeMul="2" unitType="boss" skillArr="crazy_vehicle_5,slowMove_enemy,groupSpeedUp_enemy" />
					</unitOrder>
					<![CDATA[
					<unitOrder id="boss1">
						<numberType>num</numberType>
						<unit cnName="霸空雕" unitType="boss" lifeMul="1" dpsMul="0.7" skillArr="" />
					</unitOrder>
					]]>
				</unitG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><!-- 主角限制 -->
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:weBoss</order><order>openInput</order>
						</event>
						<event>
							<order>createUnit:we2; r2</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition>say:listOver</condition>
							<order>body:大鹏; changeCamp:enemy</order>
							<order>body:大鹏; ai:patrolRandom</order>
							<order>body:大鹏; setAttackTarget:weBoss</order>
						</event>
						<event>
							<condition delay="0.1"></condition>
							<order>say; startList:s11</order>
						</event>
						<event>
							<condition delay="1"></condition>
						</event>
						<event>
							<condition  doNumber="3">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; rEnemySpace2</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:super1; rEnemySpace2</order>
						</event>
						<event>
							<condition  doNumber="3">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; rEnemySpace2</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:super1; rEnemySpace2</order>
						</event>
						<event>
							<condition doNumber="3" >enemyNumber:less_1</condition>
							<order>createUnit:enemy2; rEnemySpace2</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:boss1; rEnemySpace2</order>
						</event>
						<event>
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; weBoss</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			<level name="RaysSeaBoss">
				<!-- 关卡数据 -->
				<info enemyLv="99" preBulletArr=""  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" firstLostB="1" />
				<drop noB="1"/>
				<!-- 基本属性 -->
				<sceneLabel>RaysSea</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<unitOrder id="we1" camp="we">
						<unit id="weBoss" cnName="处决者"  lifeMul="0.18" dpsMul="500" skillArr="crazy_vehicle_8,groupLightMadFly,executionerSkill_4" />
					</unitOrder>
					<unitOrder id="enemy1">
						<numberType>num</numberType>
						<unit cnName="巨鲸" num="1" lifeMul="3"/>
						<unit cnName="大鹏" num="1" lifeMul="3"/>
						<unit cnName="潜伏者" num="1" lifeMul="3"/>
						<unit cnName="潜行者" num="1" lifeMul="3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>pro</numberType>
						<unit cnName="血蟒雇佣兵" num="2" lifeMul="3" skillArr="toFlyState,slowMove_enemy" />
						<unit cnName="血蟒突击兵" num="1" lifeMul="3" armsRange="rifleDragon" skillArr="WatchEagleWing"/>
						<unit cnName="血蟒狙击兵" num="1" lifeMul="2.5" armsRange="sniperSmilodon" skillArr="WatchEagleWing,feedback_enemy"/>
					</unitOrder>
					<unitOrder id="boss1">
						<numberType>num</numberType>
						<unit cnName="霸空雕" lifeMul="5" unitType="boss" dpsMul="1" skillArr="crazy_vehicle_3" />
					</unitOrder>
					<![CDATA[
					<unitOrder id="boss1">
						<numberType>num</numberType>
						<unit cnName="霸空雕" unitType="boss" lifeMul="1" dpsMul="0.7" skillArr="" />
					</unitOrder>
					]]>
				</unitG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><!-- 主角限制 -->
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:weBoss</order><order>openInput</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
						</event>
						<event>
							<condition  doNumber="4">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; rEnemySpace2</order>
						</event>
						<event>
							<condition  doNumber="4">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; rEnemySpace2</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:boss1; rEnemySpace2</order>
						</event>
						<event>
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; weBoss</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			
			
		</gather>
		<gather cnName="绿岛内">
			<level name="GreenIs1_plot">
				<!-- 关卡数据 -->
				<info diy="" enemyLv="99" preBulletArr=""  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" firstLostB="1" />
				<!-- 基本属性 -->
				<sceneLabel>GreenIs1</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					
					<unitOrder id="we1" camp="we">
						<![CDATA[heroSprint,jumpNumAdd1,gliding_hero_7,rolling_hero_7]]>
						<unit id="StrikerTask" cnName="我" armsRange="rifleLock,shotgunLock,sniperLock,pistolLock" lifeMul="0.06" dpsMul="1500" skillArr="addChargerMax2,heroSprint,gliding_hero_7" aiOrder="followBodyAttack:WenJieTask" dieGotoState="stru"/>
						<unit id="WenJieTask" cnName="文杰表哥" armsRange="rifleLock,shotgunLock,sniperLock,pistolLock" lifeMul="0.06" dpsMul="1500" skillArr="addChargerMax2,heroSprint,gliding_hero_7" aiOrder="followBodyAttack:StrikerTask" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="we2" camp="we">
						<unit cnName="处决者"  lifeMul="9999999" skillArr="State_noAiFind,State_InvincibleThrough,noAttackOrder,noMoveSpeed" />
					</unitOrder>
					<![CDATA[敌人属性=4*4+4*5=17]]>
					<unitOrder id="enemy1">
						<unit cnName="血蟒雇佣兵" num="2"/>
						<unit cnName="血蟒突击兵" num="1"/>
						<unit cnName="血蟒狙击兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="血蟒雇佣兵" num="1"/>
						<unit cnName="血蟒突击兵" num="2"/>
						<unit cnName="血蟒狙击兵" num="2"/>
					</unitOrder>
					<unitOrder id="boss1">
						<unit cnName="血蟒突击兵" unitType="boss" dpsMul="2"  lifeMul="0.5" onlyLevelSkillB="1" skillArr="imploding_enemy,moreMissile_enemy" />
					</unitOrder>
				</unitG>
				<rectG>
					<rect id="fly1">152,600,60,60</rect>

				</rectG> 
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><!-- 主角限制 -->
							<order>createUnit:we2; fly1</order>
							<order>body:处决者; flipToRight</order>
							
							<order>createUnit:we1; r_birth</order>
							<order>parasiticWeRolePan</order><order>openInput</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
						</event>
						<event>
							<condition  doNumber="4">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; hideRan</order>
						</event>
						<event>
							<condition  doNumber="4">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; hideRan</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:boss1; hideRan</order>
						</event>
						<event>
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; sumByP1</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="GreenIs2_plot">
				<!-- 关卡数据 -->
				<info diy="" enemyLv="99" preBulletArr=""  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" firstLostB="1" />
				<!-- 基本属性 -->
				<sceneLabel>GreenIs2</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					
					<unitOrder id="we1" camp="we">
						<![CDATA[heroSprint,jumpNumAdd1,gliding_hero_7,rolling_hero_7]]>
						<unit id="StrikerTask" cnName="我" armsRange="rifleLock,shotgunLock,sniperLock,pistolLock" lifeMul="0.06" dpsMul="1500" skillArr="addChargerMax2,heroSprint,gliding_hero_7" aiOrder="followBodyAttack:WenJieTask" warningRange="500" dieGotoState="stru"/>
						<unit id="WenJieTask" cnName="文杰表哥" armsRange="rifleLock,shotgunLock,sniperLock,pistolLock" lifeMul="0.06" dpsMul="1500" skillArr="addChargerMax2,heroSprint,gliding_hero_7" aiOrder="followBodyAttack:StrikerTask" warningRange="500" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="enemy1">
						<unit cnName="血蟒雇佣兵" num="4" dpsMul="1.5" lifeMul="2" />
						<unit cnName="血蟒突击兵" num="4" dpsMul="1.5" lifeMul="2"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="血蟒雇佣兵" num="4" dpsMul="1.5" lifeMul="2"/>
						<unit cnName="血蟒突击兵" num="4" dpsMul="1.5" lifeMul="2"/>
						<unit cnName="血蟒狙击兵" num="2" dpsMul="1.5" lifeMul="2"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="血蟒雇佣兵" num="4" dpsMul="1.5" lifeMul="2"/>
						<unit cnName="血蟒突击兵" num="4" dpsMul="1.5" lifeMul="2"/>
						<unit cnName="血蟒狙击兵" num="4" dpsMul="1.5" lifeMul="2"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="血蟒雇佣兵" dpsMul="3" lifeMul="0.5" unitType="boss" onlyLevelSkillB="1" skillArr="imploding_enemy"/>
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><!-- 主角限制 -->
							<order>createUnit:we1; r_birth</order>
							<order>addEffectDropStop:hurtMineEffect; 466,1180</order>
							<order>parasiticWeRolePan</order><order>openInput</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
						</event>
						<event>
							<condition doNumber="1"></condition>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event>
							<condition  doNumber="4">enemyNumber:less_1</condition>
							<order>createUnit:enemy2; hideRan</order>
						</event>
						<event>
							<condition  doNumber="4">enemyNumber:less_1</condition>
							<order>createUnit:enemy3; hideRan</order>
						</event>
						<event>
							<condition  doNumber="1">enemyNumber:less_1</condition>
							<order>createUnit:enemy4; hideRan</order>
						</event>
						<event>
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_birth</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; sumByP1</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="GreenIs2_plot2">
				<!-- 关卡数据 -->
				<info diy="" enemyLv="99" preSkillArr="crazy_hero_5,armsSpeed_jie_5,jumpNumAdd1,rolling_hero_7,hyperopia_hero_10,myopia_hero_10"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" firstLostB="1" />
				<!-- 基本属性 -->
				<sceneLabel>GreenIs2</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					
					<unitOrder id="we1" camp="we">
						<unit id="StrikerTask" cnName="我" armsRange="rifleLock,shotgunLock,sniperLock,pistolLock" lifeMul="0.06" dpsMul="1500" skillArr="addChargerMax2,heroSprint,gliding_hero_7" aiOrder="followBody:WenJieTask" warningRange="500" dieGotoState="stru"/>
						<unit id="WenJieTask" cnName="文杰表哥" armsRange="rifleLock,shotgunLock,sniperLock,pistolLock" lifeMul="0.06" dpsMul="1500" skillArr="addChargerMax2,heroSprint,gliding_hero_7" aiOrder="followBody:StrikerTask" warningRange="500" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="泰坦" dpsMul="0.3" lifeMul="2" unitType="boss" warningRange="400" />
					</unitOrder>
				</unitG>
				<rectG>
					<rect id="rboss1">-451,1114,323,74</rect>
					<rect id="rboss2">3159,1086,230,75</rect>

				</rectG> 
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><!-- 主角限制 -->
							<order>createUnit:we1; r_birth</order>
							<order>createUnit:enemy4; rboss2</order>
							<order>parasiticWeRolePan</order>
							<order>body:sumByP1; addTempDevice:hurtMine_6,70</order>
							<order>openInput</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
						</event>
						<event>
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>allWeHeroRebirth</order>
							<order>body:StrikerTask; addSkill:crazy_hero_5,jumpNumAdd1,rolling_hero_7,hyperopia_hero_10</order>
							<order>body:WenJieTask; addSkill:armsSpeed_jie_5,jumpNumAdd1,rolling_hero_7,myopia_hero_10</order>
						</event>
						<event>
							<condition delay="0.1"></condition><order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>body:StrikerTask; doSkill:crazy_hero_5</order>
							<order>body:WenJieTask; doSkill:rolling_hero_7</order>
							<order>say; startList:s3</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event>
							<condition delay="120"></condition>
							<order>body:泰坦; warningRange:4000</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; sumByP1</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="GreenIs3_plot">
				<!-- 关卡数据 -->
				<info diy="" enemyLv="99" preSkillArr=""  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" firstLostB="1" />
				<!-- 基本属性 -->
				<sceneLabel>GreenIs3</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					
					<unitOrder id="we1" camp="we">
						<![CDATA[解锁翻滚，二段跳，狂暴，加速扳机]]>
						<unit id="StrikerTask" cnName="我" armsRange="rifleLock,shotgunLock,sniperLock,pistolLock" lifeMul="0.05" dpsMul="2500" skillArr="crazy_hero_5,hyperopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7" aiOrder="followBodyAttack:WenJieTask" dieGotoState="stru"/>
						<unit id="WenJieTask" cnName="文杰表哥" armsRange="rifleLock,shotgunLock,sniperLock,pistolLock" lifeMul="0.05" dpsMul="2500" skillArr="armsSpeed_jie_5,myopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7" aiOrder="followBodyAttack:StrikerTask" dieGotoState="stru"/>
					</unitOrder>
					
					<unitOrder id="enemy1">
						<unit cnName="巡逻狗" num="3"/>
						<unit cnName="飞天狗" num="3"/>
						<unit cnName="血蟒雇佣兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="巡逻狗" num="4"/>
						<unit cnName="飞天狗" num="3"/>
						<unit cnName="血蟒突击兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="巡逻狗" num="4"/>
						<unit cnName="飞天狗" num="4"/>
						<unit cnName="血蟒狙击兵" num="2"/>
					</unitOrder>
					
					<unitOrder id="boss1">
						<unit cnName="血蟒狙击兵" unitType="boss" dieGotoState="stru"  onlyLevelSkillB="1" skillArr="sumLandDog,slowMove_enemy"/>
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event><order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><order>createUnit:we1; r_birth</order><order>parasiticWeRolePan</order><order>openInput</order></event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
						</event>
						<event><condition  doNumber="3">liveEnemyNumber:less_1</condition><order>createUnit:enemy1; hideRan</order></event>
						<event><condition  doNumber="3">liveEnemyNumber:less_1</condition><order>createUnit:enemy2; hideRan</order></event>
						<event><condition  doNumber="3">liveEnemyNumber:less_1</condition><order>createUnit:enemy3; hideRan</order></event>
						<event><condition  doNumber="1">liveEnemyNumber:less_1</condition><order>createUnit:boss1; hideRan</order></event>
							
						<event>
							<condition delay="0.5">liveEnemyNumber:less_1</condition>
							<order>allWeHeroRebirth</order>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
						</event>
						<event>
							<condition>hitMapRect:shop; sumByP1</condition>
							<order>say; startList:s3</order>
						</event>
						<event>
							<condition delay="1.5">say:listOver</condition>
							<order>say; startList:s4</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_birth</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; sumByP1</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="GreenIs3_domi"><![CDATA[拯救多米诺]]>
				<!-- 关卡数据 -->
				<info diy="" enemyLv="99" preSkillArr=""  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" firstLostB="1" />
				<!-- 基本属性 -->
				<sceneLabel>GreenIs3</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					
					<unitOrder id="we1" camp="we">
						<![CDATA[解锁翻滚，二段跳，狂暴，加速扳机]]>
						<unit id="StrikerTask" cnName="我" armsRange="rifleLock,shotgunLock,sniperLock,pistolLock" lifeMul="0.05" dpsMul="2500" skillArr="crazy_hero_5,hyperopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7" aiOrder="followBodyAttack:WenJieTask" dieGotoState="stru"/>
						<unit id="WenJieTask" cnName="文杰表哥" armsRange="rifleLock,shotgunLock,sniperLock,pistolLock" lifeMul="0.05" dpsMul="2500" skillArr="armsSpeed_jie_5,myopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7" aiOrder="followBodyAttack:StrikerTask" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="we2" camp="we">
						<unit cnName="多米诺" armsRange="pistolLock" lifeMul="0.07" dpsMul="1500" skillArr="" aiOrder="followBodyAttack:StrikerTask"/>
					</unitOrder>
					<unitOrder id="enemy1">
						<unit cnName="巡逻狗" num="1"/>
						<unit cnName="飞天狗" num="1"/>
						<unit cnName="血蟒雇佣兵" num="1"/>
						<unit cnName="血蟒重甲兵" num="2"/>
					</unitOrder>
					
					<unitOrder id="enemy2">
						<unit cnName="巡逻狗" num="3"/>
						<unit cnName="飞天狗" num="3"/>
						<unit cnName="血蟒突击兵" num="1"/>
						<unit cnName="血蟒重甲兵" num="2"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="巡逻狗" num="3"/>
						<unit cnName="飞天狗" num="3"/>
						<unit cnName="血蟒狙击兵" num="1"/>
						<unit cnName="血蟒重甲兵" num="2"/>
					</unitOrder>
					
					<unitOrder id="boss1">
						<unit cnName="血蟒重甲兵" unitType="boss" onlyLevelSkillB="1" skillArr="feedback_enemy,sumAirDog,imploding_enemy"/>
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event><order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><order>createUnit:we1; r_birth</order><order>parasiticWeRolePan</order><order>openInput</order></event>
						<event>
							<condition delay="0.1"></condition>
							<order>createUnit:enemy1; r3</order>
							<order>createUnit:we2; r3</order>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
						</event>
						<event><condition  doNumber="3">liveEnemyNumber:less_1</condition><order>createUnit:enemy2; hideRan</order></event>
						<event><condition  doNumber="3">liveEnemyNumber:less_1</condition><order>createUnit:enemy3; hideRan</order></event>
						<event><condition  doNumber="1">liveEnemyNumber:less_1</condition><order>createUnit:boss1; hideRan</order></event>
							
						<event>
							<condition delay="0.5">liveEnemyNumber:less_1</condition>
							<order>allWeHeroRebirth</order>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; sumByP1</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
					<group>
						<event id="e_fail2"><condition delay="1">bodyEvent:die; 多米诺</condition><order>alert:yes; 多米诺倒下，任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			
			<level name="GreenIs1_clearMine"><![CDATA[拆弹专家]]>
				<!-- 关卡数据 -->
				<info diy="greenIsTask" enemyLv="99" preBulletArr="hurtMineBullet,hammerMineBullet"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" firstLostB="1" />
				<drop noChargerBasinB="1"/>
				<!-- 基本属性 -->
				<sceneLabel>GreenIs1</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<unitOrder id="we1" camp="we">
						<unit cnName="多米诺" armsRange="rocket1" lifeMul="0.05" dpsMul="1500" skillArr="jumpNumAdd1,gliding_hero_7"/>
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><!-- 主角限制 -->
							<order>createUnit:we1; r_birth</order>
							<order>heroEverParasitic:多米诺</order>
							<order>body:sumByP1; addTempDevice:hammerMine_6,60</order>
							<order>body:sumByP1; allArmsCapacity:11</order>
							<order>body:sumByP1; allArmsChargerMul:0</order>
							<order>openInput</order>
						</event>
						<event>
							<condition delay="0.1"></condition>
							<order>GreenIs1_mine</order>
						</event>
						<event>
							<condition delay="0.1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
						</event>
						<event>
							<condition delay="0.5" doNumber="1">task:state; now:complete</condition>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; sumByP1</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			
			<level name="GreenIs4_plot"><![CDATA[掠袭大地]]>
				<!-- 关卡数据 -->
				<info diy="" enemyLv="99" preSkillArr="hiding_hero,moreMissile_hero,through_hero,underMurderous_jie,feedback_hero,charm_hero"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" firstLostB="1" />
				<!-- 基本属性 -->
				<sceneLabel>GreenIs4</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					
					<unitOrder id="we1" camp="we">
						<unit id="StrikerTask" cnName="我" armsRange="rifleLock,shotgunLock,sniperLock,pistolLock" warningRange="500" lifeMul="0.09" dpsMul="2500" skillArr="crazy_hero_10,hyperopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7" aiOrder="followBodyAttack:WenJieTask" dieGotoState="stru"/>
						<unit id="WenJieTask" cnName="文杰表哥" armsRange="rifleLock,shotgunLock,sniperLock,pistolLock" warningRange="500" lifeMul="0.09" dpsMul="2500" skillArr="armsSpeed_jie_8,myopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7" aiOrder="followBodyAttack:StrikerTask" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="we2" camp="we">
						<unit cnName="多米诺" armsRange="pistolLock" lifeMul="0.09" dpsMul="1500" skillArr="" aiOrder="followBodyAttack:sumByP1" warningRange="500"  dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="boss1">
						<unit cnName="大地掠袭者" dpsMul="1" lifeMul="1" unitType="boss" warningRange="400" onlyLevelSkillB="1" skillArr="defence100,floorTankSkill_8,sumAirDog"/>
					</unitOrder>
				</unitG>
				<rectG>
					<rect id="rwe2">-352,1240,174,84</rect>
					<rect id="rboss2">3159,1086,230,75</rect>
				</rectG> 
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><!-- 主角限制 -->
							<order>createUnit:we1; r_birth</order>
							<order>createUnit:we2; rwe2</order>
							<order>parasiticWeRolePan</order>
							<order>openInput</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>createUnit:boss1; rboss2</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>body:sumByP1; addTempDevice:hammerMine_6,40</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s3</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
						</event>
						<event>
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>allWeHeroRebirth</order>
							<order>body:StrikerTask; addSkill:hiding_hero_6,moreMissile_hero_10,through_hero_5</order>
							<order>body:WenJieTask; addSkill:underMurderous_jie_5,feedback_hero_5,charm_hero_7</order>
						</event>
						<event>
							<condition delay="0.1"></condition><order>say; startList:s4</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
						</event>
						<event>
							<condition delay="0.1"></condition><order>say; startList:s5</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event>
							<condition delay="100"></condition>
							<order>body:大地掠袭者; warningRange:4000</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; sumByP1</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="GreenSomewhere_1"><![CDATA[天塔玄机]]>
				<!-- 关卡数据 -->
				<info enemyLv="99" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1" firstLostB="1" />
				<drop noB="1"/>
				<!-- 基本属性 -->
				<sceneLabel>GreenSomewhere</sceneLabel>
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<unitOrder id="we3" camp="we">
						<unit id="陆天启" cnName="空单位"/>
					</unitOrder>
					<unitOrder id="we1" camp="we">
						<unit cnName="沃龙"/>
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>createUnit:we3; r_birth</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>body:我;noExist</order>
							<order>body:空单位;noExist</order>
							<order>body:空单位;setXY:539,474</order>
							<order>addNormalEffect:Wanda/lu;539,474:L_enemy,,die</order>
							<order>toPlotMode</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
							<order>openInput</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; win</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="timeCapsule1"><![CDATA[尸变]]>
				<!-- 关卡数据 -->
				<info diy="" enemyLv="99" preSkillArr="timeCapsuleT"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" firstLostB="1" />
				<!-- 基本属性 -->
				<sceneLabel>GreenIs3</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					
					<unitOrder id="we1" camp="we">
						<![CDATA[解锁新技能]]>
						<unit id="StrikerTask" cnName="我" armsRange="rifleYellow,shotgunCrocodile,sniperSmilodon,pistolGod" lifeMul="0.05" dpsMul="2500" skillArr="reduceBulletHurtBy,shootSpeed2,crazy_hero_5,hyperopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7,hiding_hero_6,moreMissile_hero_10,through_hero_5" aiOrder="followBodyAttack:WenJieTask" dieGotoState="stru"/>
						<unit id="WenJieTask" cnName="文杰表哥" armsRange="rifleYellow,shotgunCrocodile,sniperSmilodon,pistolGod" lifeMul="0.05" dpsMul="2500" skillArr="reduceBulletHurtBy,shootSpeed2,armsSpeed_jie_5,myopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7,underMurderous_jie_5,feedback_hero_5,charm_hero_7" aiOrder="followBodyAttack:StrikerTask" dieGotoState="stru"/>
						<unit cnName="多米诺" armsRange="pistolRed" lifeMul="0.05" dpsMul="1500" skillArr="reduceBulletHurtBy" aiOrder="followBodyAttack:StrikerTask" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="enemy1">
						<unit cnName="棍棒僵尸" num="6"/>
						<unit cnName="古惑僵尸" num="6"/>
						<unit cnName="伐木僵尸" num="3"/>
					</unitOrder>
					
					<unitOrder id="enemy2">
						<unit cnName="棍棒僵尸" num="8"/>
						<unit cnName="古惑僵尸" num="8"/>
						<unit cnName="伐木僵尸" num="5"/>
					</unitOrder>
					
					<unitOrder id="boss1">
						<unit cnName="古惑僵尸" unitType="boss" dpsMul="1" lifeMul="0.5"/>
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event><order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><order>createUnit:we1; r_birth</order><order>parasiticWeRolePan</order><order>openInput</order></event>
						<event><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>
						
						<event><order>say; startList:s2</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>
							
						<event><condition  doNumber="3">liveEnemyNumber:less_1</condition><order>createUnit:enemy1; hideRan</order></event>
						<event><condition  doNumber="3">liveEnemyNumber:less_1</condition><order>createUnit:enemy2; hideRan</order></event>
						<event><condition  doNumber="1">liveEnemyNumber:less_1</condition><order>createUnit:boss1; hideRan</order></event>
							
						<event>
							<condition delay="0.5">liveEnemyNumber:less_1</condition>
							<order>allWeHeroRebirth</order>
							<order>say; startList:s3</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>body:sumByP1; addTempDevice:timeCapsuleT_4,5</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; sumByP1</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			<level name="timeCapsule11"><![CDATA[悬停沙漏一]]>
				<!-- 关卡数据 -->
				<info diy="" enemyLv="99" preSkillArr="timeCapsuleT"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" firstLostB="1" />
				<!-- 基本属性 -->
				<sceneLabel>GreenIs4</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal"  noSuperB="1"></allDefault>
					
					<unitOrder id="we1" camp="we">
						<![CDATA[解锁新技能]]>
						<unit id="StrikerTask" cnName="我" armsRange="rifleYellow,shotgunCrocodile,sniperSmilodon,pistolGod" lifeMul="0.01" dpsMul="2500" skillArr="reduceBulletHurtBy,shootSpeed2,crazy_hero_5,hyperopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7,hiding_hero_6,moreMissile_hero_10,through_hero_5" aiOrder="no" dieGotoState="stru"/>
						<unit id="WenJieTask" cnName="文杰表哥" armsRange="rifleYellow,shotgunCrocodile,sniperSmilodon,pistolGod" lifeMul="0.01" dpsMul="2500" skillArr="reduceBulletHurtBy,shootSpeed2,armsSpeed_jie_5,myopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7,underMurderous_jie_5,pioneer_hero_5,charm_hero_7" aiOrder="no" dieGotoState="stru"/>
						<unit cnName="多米诺" armsRange="pistolRed" lifeMul="0.01" dpsMul="1500" skillArr="reduceBulletHurtBy" aiOrder="no" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="boss1">
						<unit cnName="古惑僵尸" dpsMul="1" lifeMul="20" warningRange="400" />
					</unitOrder>
					<unitOrder id="super1">
						<unit num="5" unitType="super" cnName="伐木僵尸" dpsMul="2" lifeMul="4"  onlyLevelSkillB="1" skillArr="State_AddMove,hammer_enemy,State_SpellImmunity,enemyEmp" />
					</unitOrder>
				</unitG>
				<rectG>
					<rect id="rboss2">3315,1275,20,20</rect>
					<rect id="rOver2">10,1141,72,192</rect>
					<rect id="rSuper1">3585,1177,323,156</rect>
				</rectG> 
				<eventG>
					<group>
						<event><order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><order>createUnit:we1; r_birth</order><order>parasiticWeRolePan</order><order>openInput</order></event>
						<event>
							<order>body:sumByP1; addTempDevice:timeCapsuleT_4,10</order>
							<order>createUnit:boss1; rboss2</order>
							<order>timeCapsule11_mine</order>
						</event>
						<event><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>
						<event>
							<condition>mineNumber:less_40</condition>
							<order>sceneScale:0.50</order><order>playSound:uiSound/giveupTask</order>
						</event>
						<event>
							<condition>mineNumber:less_5</condition>
							<order>sceneScale:1</order>
						</event>
						<![CDATA[没有地雷 且没有敌人时，才能通关]]>
						<event>
							<condition delay="0.5">mineNumber:less_1</condition>
							<order>body:StrikerTask; ai:patrolRandom</order>
							<order>body:WenJieTask; ai:patrolRandom</order>
							<order>body:多米诺; ai:patrolRandom</order>
							<order>say; startList:s2</order>
						</event>
						<event><condition delay="0.1">say:listOver</condition></event>
						<event>
							<condition>liveEnemyNumber:less_1</condition>
							<order>createUnit:super1; rSuper1</order>
						</event>
						<event>
							<condition>liveEnemyNumber:less_1</condition>
							<order>allWeHeroRebirth</order>
							<order>body:StrikerTask; followBody:sumByP1</order>
							<order>body:WenJieTask; followBody:sumByP1</order>
							<order>body:多米诺; followBody:sumByP1</order>
							<order>say; startList:s3</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:rOver2</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; sumByP1</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			
			<level name="timeCapsule2"><![CDATA[悬停沙漏二]]>
				<!-- 关卡数据 -->
				<info diy="" enemyLv="99" preSkillArr="timeCapsuleT"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" firstLostB="1" />
				<!-- 基本属性 -->
				<sceneLabel>GreenIs4</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					
					<unitOrder id="we1" camp="we">
						<![CDATA[解锁新技能]]>
						<unit id="StrikerTask" cnName="我" armsRange="rifleYellow,shotgunCrocodile,sniperSmilodon,pistolGod" lifeMul="0.05" dpsMul="2500" skillArr="reduceBulletHurtBy,shootSpeed2,crazy_hero_10,hyperopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7,hiding_hero_6,moreMissile_hero_10,through_hero_5" aiOrder="followBodyAttack:WenJieTask" dieGotoState="stru"/>
						<unit id="WenJieTask" cnName="文杰表哥" armsRange="rifleYellow,shotgunCrocodile,sniperSmilodon,pistolGod" lifeMul="0.05" dpsMul="2500" skillArr="reduceBulletHurtBy,shootSpeed2,armsSpeed_jie_5,myopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7,underMurderous_jie_5,pioneer_hero_5,charm_hero_7" aiOrder="followBodyAttack:StrikerTask" dieGotoState="stru"/>
						<unit cnName="多米诺" armsRange="pistolRed" lifeMul="0.05" dpsMul="1500" skillArr="reduceBulletHurtBy" aiOrder="followBodyAttack:StrikerTask" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="boss1">
						<unit cnName="棍棒僵尸" unitType="boss" dpsMul="4" lifeMul="7" warningRange="400" onlyLevelSkillB="1" skillArr="sumFoggyX" />
					</unitOrder>
				</unitG>
				<rectG>
					<rect id="rboss2">3159,1086,230,75</rect>
				</rectG> 
				<eventG>
					<group>
						<event><order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><order>createUnit:we1; r_birth</order><order>parasiticWeRolePan</order><order>openInput</order></event>
						<event>
							<order>body:sumByP1; addTempDevice:timeCapsuleT_4,5</order>
							<order>createUnit:boss1; rboss2</order>
						</event>
							
						<event>
							<condition delay="0.5">liveEnemyNumber:less_1</condition>
							<order>allWeHeroRebirth</order>
							<order>say; startList:s3</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; sumByP1</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="timeCapsule3"><![CDATA[悬停沙漏三]]>
				<!-- 关卡数据 -->
				<info diy="" enemyLv="99" preSkillArr="timeCapsuleT"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" firstLostB="1" />
				<!-- 基本属性 -->
				<sceneLabel>GreenTown1</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal"  noSuperB="1"></allDefault>
					
					<unitOrder id="we1" camp="we">
						<![CDATA[解锁新技能]]>
						<unit id="StrikerTask" cnName="我" armsRange="rifleYellow,shotgunCrocodile,sniperSmilodon,pistolGod" warningRange="1400" lifeMul="0.05" dpsMul="2500" skillArr="reduceBulletHurtBy,shootSpeed2,crazy_hero_10,hyperopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7,hiding_hero_6,moreMissile_hero_10,through_hero_5" aiOrder="followBodyAttack:WenJieTask" dieGotoState="stru"/>
						<unit id="WenJieTask" cnName="文杰表哥" armsRange="rifleYellow,shotgunCrocodile,sniperSmilodon,pistolGod" warningRange="1400" lifeMul="0.05" dpsMul="2500" skillArr="reduceBulletHurtBy,shootSpeed2,armsSpeed_jie_5,myopia_hero_10,addChargerMax2,jumpNumAdd1,heroSprint,gliding_hero_7,rolling_hero_7,underMurderous_jie_5,pioneer_hero_5,charm_hero_7" aiOrder="followBodyAttack:StrikerTask" dieGotoState="stru"/>
						<unit cnName="多米诺" armsRange="pistolRed" lifeMul="0.05" dpsMul="1500" skillArr="reduceBulletHurtBy" warningRange="1400" aiOrder="followBodyAttack:StrikerTask" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="enemy1">
						<unit cnName="血蟒感染者" dpsMul="1.4" lifeMul="6" warningRange="1400" armsRange="lightCone" skillArr="through_enemy" />
					</unitOrder>
				</unitG>
				<rectG>
					<rect id="e1">2333,809,10,10</rect>
					<rect id="e2">2477,749,10,10</rect>
					<rect id="e3">2621,644,10,10</rect>
					<rect id="e4">2797,644,10,10</rect>
					<rect id="e5">3078,723,10,10</rect>
					<rect id="e6">2943,821,10,10</rect>
					<rect id="e7">2806,906,10,10</rect>
					<rect id="e8">2688,988,10,10</rect>
					<rect id="e9">2564,1083,10,10</rect>
					<rect id="e10">2390,1193,10,10</rect>
					<rect id="e11">2537,1279,10,10</rect>
					<rect id="e12">2683,1327,10,10</rect>
					<rect id="e13">2874,1327,10,10</rect>
					<rect id="e14">3037,1327,10,10</rect>
					<rect id="e15">3200,1327,10,10</rect>
					<rect id="e16">2715,664,10,10</rect>
					<rect id="e17">3000,763,10,10</rect>
					<rect id="e18">2866,856,10,10</rect>
					<rect id="e19">2746,939,10,10</rect>
					<rect id="e20">2614,1309,10,10</rect>
					<rect id="e21">2795,1309,10,10</rect>
					<rect id="e22">2953,1309,10,10</rect>
					<rect id="e23">3120,1309,10,10</rect>
				</rectG> 
				<eventG>
					<group>
						<event><order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><order>createUnit:we1; r_birth</order><order>parasiticWeRolePan</order><order>openInput</order></event>
						<event>
							<order>body:sumByP1; addTempDevice:timeCapsuleT_4,5</order>
							<order>createUnit:enemy1; e1</order>
							<order>createUnit:enemy1; e2</order>
							<order>createUnit:enemy1; e3</order>
							<order>createUnit:enemy1; e4</order>
							<order>createUnit:enemy1; e5</order>
							<order>createUnit:enemy1; e6</order>
							<order>createUnit:enemy1; e7</order>
							<order>createUnit:enemy1; e8</order>
							<order>createUnit:enemy1; e9</order>
							<order>createUnit:enemy1; e10</order>
							<order>createUnit:enemy1; e11</order>
							<order>createUnit:enemy1; e12</order>
							<order>createUnit:enemy1; e13</order>
							<order>createUnit:enemy1; e14</order>
							<order>createUnit:enemy1; e15</order>
							
							<order>createUnit:enemy1; e16</order>
							<order>createUnit:enemy1; e17</order>
							<order>createUnit:enemy1; e18</order>
							<order>createUnit:enemy1; e19</order>
							<order>createUnit:enemy1; e20</order>
							<order>createUnit:enemy1; e21</order>
							<order>createUnit:enemy1; e22</order>
							<order>createUnit:enemy1; e23</order>
						</event>
						<event><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition><order>sceneScale:0.66</order></event>	
						<event>
							<condition delay="0.5">liveEnemyNumber:less_1</condition>
							<order>sceneScale:1</order>
							<order>allWeHeroRebirth</order>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>say; startList:s3</order>
						</event>
						
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_birth</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; sumByP1</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="GreenTown1_plot"><![CDATA[交叉骨]]>
				<!-- 关卡数据 -->
				<info diy="" enemyLv="99" noTreasureB="1" />
				<!-- 基本属性 -->
				<sceneLabel>GreenTown1</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="boss1">
						<unit cnName="交叉骨" dpsMul="1" lifeMul="1" unitType="boss" dieGotoState="stru"/>
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>	
						<event>
							<order>createUnit:boss1; hideRan</order>
						</event>
						<event>
							<condition delay="0.5">liveEnemyNumber:less_1</condition>
							<order>level;taskTimingB:false</order>
							<order>allPartnerRebirth</order>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
							<order>level; win</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		
		<gather cnName="绿岛关卡">
			<level name="GreenIs1_1">
				<info enemyLv="99" diff="2" />
				<sceneLabel>GreenIs1</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<unit cnName="古惑僵尸" num="3" skillArr="gridBlock" />
						<unit cnName="棍棒僵尸" num="3" skillArr="gridBlock" />
						<unit cnName="伐木僵尸" num="4"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="古惑僵尸" num="3" skillArr="gridBlock" />
						<unit cnName="棍棒僵尸" num="3" skillArr="gridBlock" />
						<unit cnName="伐木僵尸" num="5"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="古惑僵尸" num="6" skillArr="gridBlock" />
						<unit cnName="棍棒僵尸" num="6" skillArr="gridBlock" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="棍棒僵尸" unitType="boss" lifeMul="3" dpsMul="2" />
					</unitOrder>
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">liveEnemyNumber:less_2</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">liveEnemyNumber:less_2</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">liveEnemyNumber:less_2</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			<level name="GreenTown1_1">
				<info enemyLv="99" diff="2" />
				<sceneLabel>GreenTown1</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<unit cnName="棍棒僵尸" num="4"/>
						<unit cnName="伐木僵尸" num="6"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="棍棒僵尸" num="5"/>
						<unit cnName="伐木僵尸" num="4"/>
						<unit cnName="血蟒感染者" num="3"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="血蟒感染者" num="6"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="交叉骨" unitType="boss" lifeMul="3" dpsMul="2"/>
					</unitOrder>
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">liveEnemyNumber:less_2</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">liveEnemyNumber:less_2</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">liveEnemyNumber:less_2</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
			
			
			<level name="GreenTown2_1">
				<info enemyLv="99" diff="2" />
				<sceneLabel>GreenTown2</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<unit cnName="棍棒僵尸" num="4"/>
						<unit cnName="伐木僵尸" num="6"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="棍棒僵尸" num="5"/>
						<unit cnName="伐木僵尸" num="4"/>
						<unit cnName="血蟒感染者" num="3"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="血蟒感染者" num="6"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="交叉骨" unitType="boss" lifeMul="3" dpsMul="2"/>
					</unitOrder>
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">liveEnemyNumber:less_2</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">liveEnemyNumber:less_2</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">liveEnemyNumber:less_2</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
	</father>
		
</data>