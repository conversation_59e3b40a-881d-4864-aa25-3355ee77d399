#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SWF自动替换助手 - 生成操作指令
"""

import os
import time

def generate_quick_guide():
    """生成快速操作指南"""
    
    # 核心文件列表（按重要性排序）
    core_files = [
        ("248", "goodsClass", "主商品配置 - 所有物品免费"),
        ("74", "tenCoinClass", "十年币商品 - 免费购买"),
        ("107", "blackMarketClass", "黑市配置 - 免费购买"),
        ("247", "cheatingClass", "作弊配置 - 开启作弊"),
        ("360", "scoreGoodsClass", "积分商品 - 新增商品"),
        ("345", "giftClass", "礼包配置 - 新增礼包")
    ]
    
    print("=== 🚀 SWF快速替换指南 ===\n")
    print("【核心文件 - 必须替换】")
    
    for i, (file_id, class_name, description) in enumerate(core_files, 1):
        print(f"{i}. 搜索: {file_id}")
        print(f"   找到: DefineBinaryData (chid: {file_id}, cls: XMLOut_{class_name})")
        print(f"   右键替换: {file_id}_XMLOut_{class_name}.bin")
        print(f"   功能: {description}")
        print()
    
    print("=== ⚡ 超快速操作流程 ===")
    print("1. Ctrl+F 搜索 '248' → 右键替换 → 选择 248_XMLOut_goodsClass.bin")
    print("2. Ctrl+F 搜索 '74'  → 右键替换 → 选择 74_XMLOut_tenCoinClass.bin")
    print("3. Ctrl+F 搜索 '107' → 右键替换 → 选择 107_XMLOut_blackMarketClass.bin")
    print("4. Ctrl+F 搜索 '247' → 右键替换 → 选择 247_XMLOut_cheatingClass.bin")
    print("5. 保存SWF文件")
    print("\n✅ 完成！现在你已经有了完整的作弊功能！")

def generate_batch_commands():
    """生成批量操作命令"""
    
    print("\n=== 🤖 如果支持脚本操作 ===")
    print("在SWF编辑器的脚本控制台中尝试：")
    print()
    
    # 生成JavaScript风格的批量替换命令
    core_files = ["248", "74", "107", "247", "360", "345"]
    
    for file_id in core_files:
        print(f"// 替换文件 {file_id}")
        print(f"replaceDefineBinaryData({file_id}, 'binaryData1/{file_id}_XMLOut_*.bin');")
    
    print("\n注意：具体语法取决于你使用的SWF编辑器")

def create_file_mapping():
    """创建文件映射表"""
    
    print("\n=== 📋 完整文件映射表 ===")
    
    if os.path.exists("binaryData1"):
        bin_files = [f for f in os.listdir("binaryData1") if f.endswith('.bin')]
        bin_files.sort()
        
        print(f"总共 {len(bin_files)} 个文件需要替换：")
        print()
        
        for i, filename in enumerate(bin_files[:20], 1):  # 只显示前20个
            file_id = filename.split('_')[0]
            class_name = filename.replace('.bin', '').split('_XMLOut_')[1]
            print(f"{i:3d}. 搜索 '{file_id}' → 替换 {filename}")
        
        if len(bin_files) > 20:
            print(f"... 还有 {len(bin_files) - 20} 个文件")
            print("\n💡 建议：先替换前6个核心文件，其他的可以慢慢来")

def main():
    """主函数"""
    generate_quick_guide()
    generate_batch_commands()
    create_file_mapping()
    
    print("\n=== 🎯 推荐策略 ===")
    print("1. 【快速方案】只替换6个核心文件（5分钟完成）")
    print("2. 【完整方案】慢慢替换所有368个文件（需要时间）")
    print("3. 【按需方案】只替换你需要的武器/载具文件")
    print("\n大多数情况下，只需要替换核心文件就足够了！")

if __name__ == "__main__":
    main()
