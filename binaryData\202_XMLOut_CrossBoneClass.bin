<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="snake" cnName="血蟒">
		<body  shell="metal">
			<name>CrossBone</name>
			<cnName>交叉骨</cnName><headIconUrl>IconGather/CrossBone</headIconUrl>
			<raceType>human</raceType>
			<swfUrl>swf/hero/CrossBone.swf</swfUrl>
			<!-- 基本系数 -->
			<!-- 硬直比例，血量扣除到达这个百分比的多少就硬直 -->
			<lifeRatio>2.5</lifeRatio>
			<showLevel>9999</showLevel>
			<!-- 图像 -->
			<imgArr>
				standStop,standForward,standBack,standStop__squatStop
				,squatForward,squatStop,squatBack,squatStop__standStop
				,die1,die2,__stru,stru
				,throwAttack
				,jumpDown__,jumpDown,jumpUp__jumpDown,jumpUp,__jumpUp
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,__fill2_Up,fill2_Up,fill2_Up__fill2_Down,fill2_Down,fill2_Down__die2
				,BlueMotoRide,WatchEagleRide
			</imgArr>
			<lowerImgArr>
				thigh
				,leg_left_1
				,leg_left_0
				,foot_left
				,leg_right_1
				,leg_right_0
				,foot_right
			</lowerImgArr>
			<lifeBarExtraHeight>-20</lifeBarExtraHeight>
			<!-- 碰撞体积 -->
			<hitRect>-12, -90, 24, 90</hitRect><!-- 站立碰撞体积-->
			<squatHitRect>-12,-50,24,50</squatHitRect><!-- 下蹲碰撞体积-->
			<hurtRectArr>-15,-38,30,38</hurtRectArr> <!-- 要害受伤体积-->
			<hurtRectArr>-20, -110, 40, 110</hurtRectArr> <!-- 站立受伤体积-->
			<hurtRectArr>-12, -40, 24, 40</hurtRectArr> <!-- 下蹲受伤体积-->
			<!-- 运动 -->
			<maxVx>12</maxVx>
			<squatMaxVx>5</squatMaxVx>
			<!-- AI属性 -->
			<armsNumber>1</armsNumber><!-- 武器个数 -->
			<randomArmsRange>yearSheep$cb</randomArmsRange><!-- 随机枪范围，里面是armsRange的标签 -->	
			<extraAIClassLabel></extraAIClassLabel>
			<oneAiLabel>FightShooter</oneAiLabel>
			<!-- 技能 -->
			<bossSkillArr>cardNoAttackSkill,meltFlamerPurgold,noUnderMulHurt,defenceWenjie,godShield,KingRabbitTreater,treater_knights,through_hero_10,sameInvi,rideBlueMoto,rideFireWolf,rideKun,rideNian,rideFloorTank</bossSkillArr>
			<extraDropArmsB>1</extraDropArmsB>
			
			<!-- 攻击数据 -->
			<hurtArr>
				
			</hurtArr>
		</body>
		
		
		
		
	</father>	
	<father name="snake">
		
		<skill>
			<name>rideFireWolf</name><cnName>骑射专家</cnName><conditionString>0.8,1</conditionString><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>99999</cd><firstCd>99999</firstCd><ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition><otherConditionArr>targetNoVehicleB,targetLifePerRange</otherConditionArr><target>me</target>
			<addType>instant</addType><summonedUnitsB>1</summonedUnitsB>
			<effectType>rideShootVehicle</effectType><duration>89999</duration>
			<valueString>虚炎狼</valueString>
			<obj>"cnName":"虚炎狼坐骑","num":1,"lifeMul":0.15,"dpsMul":1,"mulByFatherB":1,"maxNum":1,"skillArr":["State_SpellImmunity","noUnderMulHurt","defenceWenjie"]</obj>
			<meEffectImg name="changeToGaia_me" /><description>生命值每减少20%都会召唤出不同的载具，并进入骑射状态。</description>
		</skill>
		
		<skill>
			<name>rideFloorTank</name><cnName>掠袭者骑射</cnName><conditionString>0.6,0.8</conditionString>
			<cd>99999</cd><firstCd>99999</firstCd><ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition><otherConditionArr>targetNoVehicleB,targetLifePerRange</otherConditionArr><target>me</target>
			<addType>instant</addType><summonedUnitsB>1</summonedUnitsB>
			<effectType>rideShootVehicle</effectType><duration>89999</duration>
			<valueString>大地掠袭者</valueString>
			<obj>"cnName":"大地掠袭者","num":1,"lifeMul":0.1,"dpsMul":1,"mulByFatherB":1,"maxNum":1,"skillArr":["State_SpellImmunity","noUnderMulHurt","defenceWenjie","floorTankSkill"]</obj>
			<meEffectImg name="changeToGaia_me" /><description>大地掠袭者骑射</description>
		</skill>
		
		<skill>
			<name>rideNian</name><cnName>切割者骑射</cnName><conditionString>0.4,0.6</conditionString>
			<cd>99999</cd><firstCd>99999</firstCd><ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition><otherConditionArr>targetNoVehicleB,targetLifePerRange</otherConditionArr><target>me</target>
			<addType>instant</addType><summonedUnitsB>1</summonedUnitsB>
			<effectType>rideShootVehicle</effectType><duration>89999</duration>
			<valueString>切割者</valueString>
			<obj>"cnName":"切割者","num":1,"lifeMul":0.15,"dpsMul":1,"mulByFatherB":1,"maxNum":1,"skillArr":["State_SpellImmunity","noUnderMulHurt","defenceWenjie","murderous_vehicle_10"]</obj>
			<meEffectImg name="changeToGaia_me" /><description>切割者骑射</description>
		</skill>
		<skill>
			<name>rideKun</name><cnName>鲲骑射</cnName><conditionString>0.2,0.4</conditionString>
			<cd>99999</cd><firstCd>99999</firstCd><ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition><otherConditionArr>targetNoVehicleB,targetLifePerRange</otherConditionArr><target>me</target>
			<addType>instant</addType><summonedUnitsB>1</summonedUnitsB>
			<effectType>rideShootVehicle</effectType><duration>89999</duration>
			<valueString>鲲</valueString>
			<obj>"cnName":"鲲","num":1,"lifeMul":0.1,"dpsMul":1,"mulByFatherB":1,"maxNum":1,"skillArr":["State_SpellImmunity","noUnderMulHurt","defenceWenjie"]</obj>
			<meEffectImg name="changeToGaia_me" /><description>鲲骑射</description>
		</skill>
		<![CDATA[
		<skill>
			<name>rideNian</name><cnName>年兽骑射</cnName><conditionString>0.2,0.4</conditionString>
			<cd>99999</cd><firstCd>99999</firstCd><ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition><otherConditionArr>targetNoVehicleB,targetLifePerRange</otherConditionArr><target>me</target>
			<addType>instant</addType><summonedUnitsB>1</summonedUnitsB>
			<effectType>rideShootVehicle</effectType><duration>89999</duration>
			<valueString>年兽</valueString>
			<obj>"cnName":"年兽坐骑","num":1,"lifeMul":0.3,"dpsMul":1,"mulByFatherB":1,"maxNum":1,"skillArr":["State_SpellImmunity","noUnderMulHurt"]</obj>
			<meEffectImg name="changeToGaia_me" /><description>虚炎狼骑射</description>
		</skill>
		]]>
		<skill>
			<name>rideBlueMoto</name><cnName>飞魄骑射</cnName><conditionString>0,0.2</conditionString>
			<cd>99999</cd><firstCd>99999</firstCd><ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition><otherConditionArr>targetNoVehicleB,targetLifePerRange</otherConditionArr><target>me</target>
			<addType>instant</addType><summonedUnitsB>1</summonedUnitsB>
			<effectType>rideShootVehicle</effectType><duration>89999</duration>
			<valueString>飞魄</valueString>
			<obj>"cnName":"飞魄","num":1,"lifeMul":0.2,"dpsMul":1,"mulByFatherB":1,"maxNum":1,"skillArr":["State_SpellImmunity","noUnderMulHurt","defenceWenjie","blade_blueMoto_9","shockWave_10"]</obj>
			<meEffectImg name="changeToGaia_me" /><description>飞魄骑射</description>
		</skill>
		
		
		<skill>
			<name>sameInvi</name>
			<cnName>均衡矩阵</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB><everNoClearB>1</everNoClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>sameInvi</effectType>
			<duration>9999999</duration>
			<stateEffectImg name="SaberTiger_shield_second_state"/>
			<description>目标无敌时，自身也进入无敌状态。</description>
		</skill>
		<skill>
			<name>hurtWenjie</name>
			<cnName>歧视文杰</cnName><ignoreNoSkillB>1</ignoreNoSkillB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>allHit</condition>
			<otherConditionArr>p1Wenjie</otherConditionArr>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurtNoCondition</effectType>
			<mul>2.5</mul>
			<!--图像------------------------------------------------------------ -->
			<description>对P1文杰造成[mul-1]的伤害。</description>
		</skill>
		<skill>
			<name>defenceWenjie</name>
			<cnName>防御文杰</cnName><ignoreNoSkillB>1</ignoreNoSkillB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underAllHit</condition>
			<otherConditionArr>p1Wenjie</otherConditionArr>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurtNoCondition</effectType>
			<mul>0.70</mul>
			<!--图像------------------------------------------------------------ -->
			<description>降低受到P1文杰[1-mul]的伤害。</description>
		</skill>
	</father>
</data>