=== binaryData1 批量导入优先级列表 ===

【高优先级 - 作弊相关文件】
✅ 248_XMLOut_goodsClass.bin          - 主商品配置（所有物品免费+无限购买）
✅ 74_XMLOut_tenCoinClass.bin         - 十年币商品（免费+无限购买）
✅ 360_XMLOut_scoreGoodsClass.bin     - 积分商品（新增，免费+无限购买）
✅ 107_XMLOut_blackMarketClass.bin    - 黑市配置
✅ 247_XMLOut_cheatingClass.bin       - 作弊配置
✅ 345_XMLOut_giftClass.bin           - 礼包配置（新增）

【中优先级 - 新增武器和载具】
✅ 333_XMLOut_shotgunSkunkClass.bin   - 赤鼬散弹枪（新增）
✅ 368_XMLOut_rocketCateClass.bin     - 卡特巨炮（新增）
✅ 316_XMLOut_penGunClass.bin         - 钢笔枪（新增）
✅ 317_XMLOut_flamerClass.bin         - 火焰枪（新增）
✅ 309_XMLOut_crossbowClass.bin       - 弩类武器（新增）

【低优先级 - 其他配置文件】
以下文件按编号顺序导入即可：

1_XMLOut_carBodyClass.bin
2_XMLOut_deputyTaskClass.bin
3_XMLOut_peakExpClass.bin
4_XMLOut_scene95Class.bin
5_XMLOut_activeGoodsClass.bin
6_XMLOut_YouthWolfClass.bin
7_XMLOut_greedySnakeClass.bin
8_XMLOut_bcardSkillListClass.bin
9_XMLOut__76_NianCarClass.bin
10_XMLOut__01_DaybreakClass.bin
11_XMLOut_equipRangeClass.bin
12_XMLOut_WarriorSecClass.bin
13_XMLOut__308_OreVesselClass.bin
14_XMLOut__31_BlueWhaleClass.bin
15_XMLOut__12_RedMotoClass.bin
16_XMLOut_BoomSkullSClass.bin
17_XMLOut_skillOtherConditionClass.bin
18_XMLOut_wilderLevelClass.bin
19_XMLOut_yearChickenClass.bin
20_XMLOut_fashionClass.bin
21_XMLOut_dailyGiftClass.bin
22_XMLOut_loveSkillClass.bin
23_XMLOut_activeClass.bin
24_XMLOut__72_AircraftGunClass.bin
25_XMLOut_WatchdogClass.bin
26_XMLOut_level_Hospital2_1Class.bin
27_XMLOut_partsRareProClass.bin
28_XMLOut_skyArchClass.bin
29_XMLOut_loveLevelZangShiClass.bin
30_XMLOut_lifeAskClass.bin
31_XMLOut__11_DiggersClass.bin
32_XMLOut_levelClass.bin
33_XMLOut__301_OreTaperClass.bin
34_XMLOut_DoubleZombieClass.bin
35_XMLOut_waterFlamerClass.bin
36_XMLOut_FightKingSkillClass.bin
37_XMLOut_LaoZhangClass.bin
38_XMLOut_customTaskClass.bin
39_XMLOut_lightConeClass.bin
40_XMLOut_bcardRareSkillListClass.bin
41_XMLOut__03_ProphetClass.bin
42_XMLOut_extremeLaserClass.bin
43_XMLOut__71_ThunderClass.bin
44_XMLOut_ninetyBulletClass.bin
45_XMLOut_medelPropertyClass.bin
46_XMLOut_PhantomZClass.bin
47_XMLOut__01_levelSolarClass.bin
48_XMLOut_shotgunClass.bin
49_XMLOut_closureGunClass.bin
50_XMLOut_sniperClass.bin

... (继续到368个文件)

=== 导入操作步骤 ===

1. 【备份】先备份原始SWF文件
2. 【搜索】在SWF编辑器中按Ctrl+F搜索文件编号
3. 【定位】找到对应的DefineBinaryData项
4. 【替换】右键选择"替换"，选择binaryData1中的对应文件
5. 【确认】确认导入成功
6. 【保存】完成所有导入后保存SWF文件

=== 重要提醒 ===

✅ 这是作弊版本：所有商品价格=0，购买限制=9999
✅ 包含368个配置文件，比原版多36个新文件
✅ 新增了2025年商品、新武器、新载具等内容
✅ 建议先导入高优先级文件测试效果
✅ 如果某些文件在SWF中不存在，可以跳过
