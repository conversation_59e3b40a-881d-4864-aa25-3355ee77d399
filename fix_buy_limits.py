#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修改binaryData1中所有商品的购买限制为9999
"""

import re
import os

def fix_buy_limits(file_path):
    """修复单个文件的购买限制"""
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 记录原始内容用于比较
        original_content = content
        
        # 替换所有 buyLimitNum="数字" 为 buyLimitNum="9999"
        content = re.sub(r'buyLimitNum="[0-9]+"', 'buyLimitNum="9999"', content)
        
        # 替换所有 dayBuyLimitNum="数字" 为 dayBuyLimitNum="9999"  
        content = re.sub(r'dayBuyLimitNum="[0-9]+"', 'dayBuyLimitNum="9999"', content)
        
        # 替换所有 price="数字" 为 price="0" (作弊版本应该免费)
        content = re.sub(r'price="[0-9]+"', 'price="0"', content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已修复: {file_path}")
            return True
        else:
            print(f"无需修改: {file_path}")
            return False
            
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    # 需要修复的商品配置文件
    files_to_fix = [
        "binaryData1/248_XMLOut_goodsClass.bin",
        "binaryData1/74_XMLOut_tenCoinClass.bin", 
        "binaryData1/360_XMLOut_scoreGoodsClass.bin",
        "binaryData1/107_XMLOut_blackMarketClass.bin"
    ]
    
    fixed_count = 0
    for file_path in files_to_fix:
        if fix_buy_limits(file_path):
            fixed_count += 1
    
    print(f"\n总共修复了 {fixed_count} 个文件")
    print("所有商品现在都是免费且无限购买！")

if __name__ == "__main__":
    main()
