<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="space">
		<body>
			<name>Or<PERSON><PERSON><PERSON>er</name>
			<cnName>矿场战机</cnName><lifeRatio>2</lifeRatio>
			<raceType>ship</raceType>
			<swfUrl>swf/ship/OreFighter.swf</swfUrl>
			<!-- 图像 -->
			<dieImg name="stoneBoom"/>
			<dieJumpMul>0</dieJumpMul>
			<imgArr>
				stand,normalAttack,die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-20,-20,40,40</hitRect>
			<hurtRectArr>-40,-25,80,50</hurtRectArr>
			<motionState>fly</motionState>
			<flyType>space</flyType>
			<flipCtrlBy>target</flipCtrlBy>
			<motionD F_AIR="2"/>
			<maxVx>8</maxVx>
			<skillArr>hitCheckDie</skillArr>
			<hurtArr>
				<hurt cd="3">
					<imgLabel>normalAttack</imgLabel>
					<bulletLabel>OreFighterBullet</bulletLabel>
					<grapRect>-500,-35,350,87</grapRect>
					<hurtRatio>1</hurtRatio>
				</hurt>
			</hurtArr>
		</body>
		
				<bullet>
					<name>OreFighterBullet</name>
					<cnName>矿场战机子弹</cnName>
					<hurtRatio>0.3</hurtRatio>
					<bulletLife>4</bulletLife>
					<bulletWidth>30</bulletWidth>
					<hitType>rect</hitType>
					<!--攻击时的属性------------------------------------------------------------ -->
					<attackGap>0.3</attackGap>
					<attackDelay>0.13</attackDelay>
					<bulletAngle>180</bulletAngle>
					<bulletNum>1</bulletNum>
					<shootNum>2</shootNum>
					<shootGap>0.1</shootGap>
					<shootAngle>15</shootAngle>	
					
					<penetrationGap>10000</penetrationGap>
					<!--运动属性------------------------------------------------------------ -->	
					<shootPoint>-50,20</shootPoint>
					<bulletSpeed>12</bulletSpeed>
					<bulletImgUrl name="bangerGun_bullet"/>
					<hitImgUrl name="gun_hit" />
				</bullet>
				
				
				
				
		<body>
			<name>OreBird</name>
			<cnName>鸟头机</cnName><lifeRatio>3</lifeRatio>
			<raceType>ship</raceType>
			<swfUrl>swf/ship/OreBird.swf</swfUrl>
			<!-- 图像 -->
			<dieImg name="stoneBoom"/>
			<dieJumpMul>0</dieJumpMul>
			<lockLeftB>1</lockLeftB>
			<imgArr>
				stand,normalAttack,die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-40,-25,80,50</hitRect>
			<motionState>fly</motionState>
			<flyType>space</flyType>
			<motionD F_AIR="4"/>
			<maxVx>5</maxVx>
			<skillArr>spaceMoveLeft,oreAutoAttack,hitCheckDie</skillArr>
			<hurtArr>
				<hurt cd="2">
					<imgLabel>normalAttack</imgLabel>
					<bulletLabel>OreBirdBullet</bulletLabel>
					<grapRect>-10000,-10000,20000,20000</grapRect>
					<hurtRatio>1</hurtRatio>
				</hurt>
			</hurtArr>
		</body>
		
				<bullet>
					<name>OreBirdBullet</name>
					<cnName>鸟头机-十字弹</cnName>
					<hurtRatio>0.3</hurtRatio>
					<bulletLife>4</bulletLife>
					<bulletWidth>30</bulletWidth>
					<hitType>rect</hitType>
					<!--攻击时的属性------------------------------------------------------------ -->
					<attackGap>0.4</attackGap>
					<attackDelay>0.2</attackDelay>
					<bulletAngle>0</bulletAngle>
					<bulletNum>4</bulletNum>
					<shootNum>4</shootNum>
					<shootGap>0.1</shootGap>
					<shootAngle>135</shootAngle>					
					<!--运动属性------------------------------------------------------------ -->	
					<shootPoint>5,11</shootPoint>
					<bulletSpeed>12</bulletSpeed>
					<bulletImgUrl name="PurpleBeeBullet"/>
					<hitImgUrl name="gun_hit" />
				</bullet>
	</father>
</data>