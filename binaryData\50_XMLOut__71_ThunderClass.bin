<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="car" cnName="战车定义">
		<equip name="Thunder" cnName="雷鸣" evolutionLabel="Thunderbolt">
			<evolutionLv>2</evolutionLv>
			<specialInfoArr>二段跳跃</specialInfoArr>
			<main label="Thunder_main" dpsMul="2.7" len="110"/>
			<sub label="Thunder_sub" dpsMul="2.7" len="55" minRa="174" maxRa="174.1"/>
			<lifeMul>2.5</lifeMul>
			<attackMul>1.8</attackMul>
			<duration>70</duration>
			<cd>85</cd>
			<skillArr>vehicleFit_Gaia</skillArr>
			<addObjJson>{'dpsAll':0.22,'lifeAll':0.20}</addObjJson>
		</equip>
		
		<equip name="Thunderbolt" cnName="雷霆">
			<evolutionLv>4</evolutionLv><mustCash>50</mustCash>
			<specialInfoArr>二段跳跃</specialInfoArr>
			<main label="Thunder_main" dpsMul="3" len="110"/>
			<sub label="Thunder_sub" dpsMul="3.1" len="55" minRa="174" maxRa="174.1"/>
			<lifeMul>3.2</lifeMul>
			<attackMul>2.1</attackMul>
			<duration>70</duration>
			<cd>85</cd>
			<skillArr>vehicleFit_Gaia</skillArr>
			<addObjJson>{'dpsAll':0.24,'lifeAll':0.20}</addObjJson>
		</equip>
		
		<bullet cnName="雷鸣-主炮">
			<name>Thunder_main</name>
			<cnName>雷鸣-主炮</cnName>
			<!--武器属性------------------------------------------------------------ -->
			<hurtRatio>0.57</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>1</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.3</attackGap>
			
			<!--运动属性------------------------------------------------------------ -->	
			<shootRecoil>15</shootRecoil>
			<screenShakeValue>16</screenShakeValue>
			<bulletSpeed>35</bulletSpeed>
			<boomD  bodyB="1" floorB="1" radius="120"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">rocket/rocketBullet</bulletImgUrl>
			<fireImgUrl raNum="30" soundUrl="rocket/barrel_sound" con="add">gunFire/rocket</fireImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom1"  shake="2,0.2,10">boomEffect/boom3</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="雷鸣-副炮">
			<name>Thunder_sub</name>
			<cnName>雷鸣-副炮</cnName>
			<!--武器属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>0.001</bulletLife>
			<hitType>longLine</hitType>
			<bulletWidth>500</bulletWidth>
			<bulletShakeWidth>100</bulletShakeWidth>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.1</attackGap>
			<attackDelay>0</attackDelay>
			<!--运动属性------------------------------------------------------------ -->	
			<shootRecoil>4</shootRecoil>
			<screenShakeValue>9</screenShakeValue>
			<shakeAngle>3</shakeAngle>
			<bulletSpeed>0</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<lineD lightColor="0x00FFFF" size="2" lightSize="6"/>
			<bulletImgUrl>longLine</bulletImgUrl>
			<fireImgUrl raNum="30" soundUrl="ak/barrel5_sound">gunFire/f</fireImgUrl>
			<hitImgUrl>bulletHitEffect/yellow_motion</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
	</father>
	
	
	
	
	
	
	<father name="vehicle" cnName="战车body">
		<body index="0" name="雷鸣" shell="metal">
			
			<name>Thunder</name>
			<cnName>雷鸣</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/vehicle/Thunder.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>0.5</headHurtMul>
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgClass>CarImage</imgClass>
			<imgType>normal</imgType>
			<rotateBySlopeB>1</rotateBySlopeB>
			<imgArr>
				stand,move,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-12, -90, 24, 90</hitRect><!-- 站立碰撞体积-->
			<!-- 运动 -->
			<maxVx>13</maxVx>
			<maxJumpNum>2</maxJumpNum>
			
			<!-- 技能 -->
			<attackAIClass>CarAttack_AI</attackAIClass>
			<keyClass>CarBodyKey</keyClass>
			<bulletLauncherClass>CarBulletLauncher</bulletLauncherClass>
			<skillArr></skillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>move</imgLabel>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="Titans/hit1">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpDown</imgLabel>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpDown__</imgLabel>
					<hurtRatio>0.69</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit3">bulletHitEffect/energy</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>
		<body name="雷霆" fixed="Thunder" shell="metal">
			<name>Thunderbolt</name>
			<cnName>雷霆</cnName>
			<swfUrl>swf/vehicle/Thunderbolt.swf</swfUrl>
			<bmpUrl>BodyImg/Thunderbolt</bmpUrl>
			<bossSkillArr>upperLimitSecond,slowMove_enemy,paralysis_enemy</bossSkillArr>
		</body>
	</father>
</data>